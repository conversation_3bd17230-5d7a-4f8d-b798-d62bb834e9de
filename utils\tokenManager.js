const TOKEN_KEY = 'accessToken';
const USER_INFO_KEY = 'userInfo';

// 导入统一的token解析工具
const { parseJWTToken, isTokenExpired } = require('./tokenUtils');

class TokenManager {
  constructor() {
    this.tokenKey = 'accessToken';
    this.tokenExpiryKey = 'tokenExpiry';
    this.refreshThreshold = 5 * 60 * 1000; // 5分钟阈值
  }

  // 获取token
  getToken() {
    return wx.getStorageSync(this.tokenKey);
  }

  // 保存token
  setToken(token, expiresIn = 86400) { // 86400秒 = 24小时
    const expiryTime = Date.now() + expiresIn * 1000;
    wx.setStorageSync(this.tokenKey, token);
    wx.setStorageSync(this.tokenExpiryKey, expiryTime);
  }

  // 获取用户信息
  getUserInfo() {
    return wx.getStorageSync(USER_INFO_KEY);
  }

  // 保存用户信息
  setUserInfo(userInfo) {
    wx.setStorageSync(USER_INFO_KEY, userInfo);
  }

  // 检查是否已登录
  isLoggedIn() {
    const token = this.getToken();
    if (!token) {
      return false;
    }
    
    // 检查JWT token是否过期
    try {
      if (isTokenExpired(token)) {
        // token已过期，清除登录状态
        this.clearLoginState();
        return false;
      }
      return true;
    } catch (error) {
      console.error('检查token状态失败:', error);
      // 解析失败时清除登录状态，返回false
      this.clearLoginState();
      return false;
    }
  }

  // 检查是否需要刷新（5分钟内过期）
  shouldRefresh() {
    const expiryTime = wx.getStorageSync(this.tokenExpiryKey);
    if (!expiryTime) {
      return false;
    }
    const timeLeft = expiryTime - Date.now();
    return timeLeft < this.refreshThreshold;
  }

  // 清除登录状态
  clearLoginState() {
    wx.removeStorageSync(this.tokenKey);
    wx.removeStorageSync(this.tokenExpiryKey);
    wx.removeStorageSync(USER_INFO_KEY);
  }
}

// 创建全局实例
const tokenManager = new TokenManager();

export default tokenManager; 