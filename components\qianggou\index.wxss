/* 弹窗遮罩层 */
.qianggou-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5);
  z-index: 9999;
  overflow: hidden;
}

/* 当弹窗显示时禁止页面滚动 */
.qianggou-popup-container {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: transparent;
  z-index: 10000;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: flex-end;
  transition: transform 0.3s cubic-bezier(.34,1.56,.64,1);
  transform: translateY(100%);
  overflow: hidden;
}

/* 弹窗容器 */
.qianggou-popup-container {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: transparent;
  z-index: 10000;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: flex-end;
  transition: transform 0.3s cubic-bezier(.34,1.56,.64,1);
  transform: translateY(100%);
}

.qianggou-popup-show {
  transform: translateY(0);
}

.qianggou-popup-hide {
  transform: translateY(100%);
}

/* 页面容器 */
.qianggou-page {
  position: relative;
  width: 750rpx;
  height: 900rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 25rpx 25rpx 0 0;
  background-color: #f5f5f5;
}

/* 关闭按钮 */
.qianggou-close-btn {
  position: fixed;
  top: 525rpx;
  right: 23rpx;
  z-index: 1001;
}

.qianggou-close-wrapper {
  width: 45rpx;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qianggou-close-icon {
  width: 45rpx;
  height: 45rpx;
}

/* 主要内容区域 */
.qianggou-content {
  padding: 0;
  height: 100%;
  box-sizing: border-box;
}

/* 店铺信息区域 */
.qianggou-store-info {
  margin-bottom: 16rpx;
  padding: 29rpx 19rpx 31rpx 25rpx;
  background-color: #ffffff;
}

.qianggou-store-header {
  display: flex;
  align-items: flex-start;
}

.qianggou-store-logo {
  width: 30rpx;
  height: 28rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.qianggou-store-details {
  flex: 1;
}

.qianggou-store-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  line-height: 28rpx;
  height: 28rpx;
  display: block;
  margin-bottom: 8rpx;
}

.qianggou-store-address {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: 16rpx;
}

.qianggou-address-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #666666;
  line-height: 23rpx;
  flex: 1;
}

.qianggou-distance {
  font-size: 24rpx;
  color: #999999;
  text-align: right;
  margin-left: 10rpx;
}

/* 商品信息区域 */
.qianggou-product-info {
  margin-top: 16rpx;
  margin-bottom: 17rpx;
  padding-top: 27rpx;
  padding-left: 24rpx;
  background-color: #ffffff;
}

.qianggou-product-item {
  margin-bottom: 20rpx;
}

.qianggou-product-content {
  display: flex;
  align-items: center;
}

.qianggou-product-image {
  width: 120rpx;
  height: 120rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.qianggou-product-details {
  flex: 1;
  margin-top: 2rpx;
}

.qianggou-product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  line-height: 28rpx;
  margin-bottom: 10rpx;
  display: block;
  text-align: left;
  vertical-align: top;
}

.qianggou-product-price {
  display: flex;
  align-items: baseline;
}

.qianggou-price-symbol {
  font-size: 24rpx;
  color: #ff4444;
  margin-right: 4rpx;
}

.qianggou-price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff4444;
}

.qianggou-discount-info {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx 20rpx 20rpx 3rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60rpx;
}

.qianggou-discount-tag {
  font-size: 24rpx;
  color: #333333;
  font-weight: 600;
  line-height: 25rpx;
}

.qianggou-discount-right {
  display: flex;
  align-items: center;
  margin-right: 39rpx;
}

.qianggou-discount-desc {
  font-size: 24rpx;
  color: #FF3213;
  border: 1rpx solid #FF3213;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.qianggou-discount-icon {
  width: 11rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

/* 套餐区域 */
.qianggou-package-info {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  position: relative;
}

.qianggou-package-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.qianggou-package-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-right: 20rpx;
}

.qianggou-package-badge {
  width: 23rpx;
  height: 23rpx;
  margin-right: 20rpx;
}

.qianggou-package-right {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.qianggou-limit-tag {
  font-size: 24rpx;
  color: #FF3213;
  margin-right: 10rpx;
  height: 23rpx;
  line-height: 23rpx;
  display: flex;
  align-items: center;
  font-weight: 500;
}

.qianggou-countdown {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
  margin-right: -6rpx;
  height: 23rpx;
  line-height: 23rpx;
  display: flex;
  align-items: center;
}

.qianggou-package-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 160rpx;
  padding: 20rpx;
  position: relative;
}

.qianggou-package-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  object-fit: cover;
}

.qianggou-package-left {
  flex: 1;
  position: relative;
  z-index: 2;
}

.qianggou-price-row {
  display: flex;
  align-items: center;
  margin-top: 10rpx;
}

.qianggou-package-desc {
  font-size: 24rpx;
  color: #ffffff;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: block;
}

.qianggou-original-price {
  font-size: 28rpx;
  color: #FFDE00;
}

.qianggou-package-right {
  text-align: center;
  margin-right: 20rpx;
  position: relative;
  z-index: 2;
}

.qianggou-package-price-section {
  text-align: center;
  margin-right: 20rpx;
  position: relative;
  z-index: 2;
}

.qianggou-discount-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFDE00;
  display: block;
  margin-bottom: 5rpx;
}

.qianggou-per-time {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.qianggou-per-time-label {
  font-size: 20rpx;
  color: #ffffff;
  margin-bottom: 2rpx;
}

.qianggou-per-time-price {
  font-size: 24rpx;
  color: #FFDE00;
  font-weight: bold;
}

.qianggou-package-image {
  width: 80rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

/* 支付按钮 */
.qianggou-pay-btn {
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 35rpx;
  padding: 24rpx 32rpx;
  text-align: center;
  margin: 40rpx 30rpx 30rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(63, 103, 255, 0.2);
}

.qianggou-pay-content {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
}

.qianggou-pay-text {
  font-size: 32rpx;
  font-weight: 600;
  margin-right: 10rpx;
}

.qianggou-pay-symbol {
  font-size: 24rpx;
  margin-right: 4rpx;
}

.qianggou-pay-price {
  font-size: 32rpx;
  font-weight: 600;
} 