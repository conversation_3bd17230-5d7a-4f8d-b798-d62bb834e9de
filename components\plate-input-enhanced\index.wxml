<!--components/plate-input-enhanced/index.wxml-->
<view class="plate-input-enhanced">
  <!-- 车牌输入区域 -->
  <view class="plate-form-section">
    <view class="form-card">
      <view class="form-title">输入车牌自动识别车型</view>
      <view class="form-hint">车牌信息仅用于车型适配,紫太郎会严格保护您的信息</view>
      
      <!-- 车牌输入框 -->
      <view class="plate-input-container">
        <view class="plate-input-group">
          <!-- 省份输入框 -->
          <view class="plate-input {{focusStates[0] ? 'active' : ''}} {{plateNumber.province ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="0">
            <text wx:if="{{plateNumber.province}}">{{plateNumber.province}}</text>
            <text wx:else class="placeholder">省</text>
          </view>
          
          <!-- 字母输入框 -->
          <view class="plate-input {{focusStates[1] ? 'active' : ''}} {{plateNumber.letter ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="1">
            <text wx:if="{{plateNumber.letter}}">{{plateNumber.letter}}</text>
            <text wx:else class="placeholder">市</text>
          </view>
          
          <!-- 分隔点 -->
          <view class="plate-separator">·</view>
          
          <!-- 后续输入框 -->
          <view class="plate-input {{focusStates[2] ? 'active' : ''}} {{plateNumber.letter2 ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="2" style="margin-left: 16rpx;">
            <text wx:if="{{plateNumber.letter2}}">{{plateNumber.letter2}}</text>
          </view>
          
          <view class="plate-input {{focusStates[3] ? 'active' : ''}} {{plateNumber.letter3 ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="3">
            <text wx:if="{{plateNumber.letter3}}">{{plateNumber.letter3}}</text>
          </view>
          
          <view class="plate-input {{focusStates[4] ? 'active' : ''}} {{plateNumber.letter4 ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="4">
            <text wx:if="{{plateNumber.letter4}}">{{plateNumber.letter4}}</text>
          </view>
          
          <view class="plate-input {{focusStates[5] ? 'active' : ''}} {{plateNumber.letter5 ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="5">
            <text wx:if="{{plateNumber.letter5}}">{{plateNumber.letter5}}</text>
          </view>
          
          <view class="plate-input {{focusStates[6] ? 'active' : ''}} {{plateNumber.letter6 ? 'filled' : ''}}" 
                bindtap="onInputTap" data-index="6">
            <text wx:if="{{plateNumber.letter6}}">{{plateNumber.letter6}}</text>
          </view>
          
          <!-- 新能源框 -->
          <view class="new-energy-box" bindtap="onInputTap" data-index="7">
            <text wx:if="{{plateNumber.newEnergy}}" class="new-energy-input">{{plateNumber.newEnergy}}</text>
            <text wx:else class="new-energy-text">新能源</text>
          </view>
        </view>
      </view>



      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="action-btn" bindtap="showHistory" wx:if="{{showHistory && historyPlates.length > 0}}">
          <text class="action-text">历史记录</text>
        </view>
      </view>

      <!-- 用户协议 -->
      <view class="agreement-section">
        <view class="agreement-checkbox" bindtap="onAgreementToggle">
          <image wx:if="{{agreed}}" src="https://oss.csdu.net/ztl/images/arrow_left.png" class="checkbox-icon"></image>
          <view wx:else class="checkbox-empty"></view>
        </view>
        <text class="agreement-text">我已阅读并同意</text>
        <text class="agreement-link" bindtap="onAgreementLink">《用户信息授权说明》</text>
      </view>

      <!-- 提交按钮 -->
      <button class="submit-btn {{agreed && plateValid ? 'active' : 'disabled'}}" 
              bindtap="onSubmit" disabled="{{!agreed || !plateValid}}">
        同意授权并识别
      </button>
    </view>
  </view>

  <!-- 其他添加方式 -->
  <view class="alternative-section">
    <text class="alternative-title">选择其他添加爱车方式</text>
    
    <view class="alternative-btn" bindtap="onManualSelect">
      <text class="btn-text">手动选择车型</text>
      <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="btn-arrow"></image>
    </view>
    
    <view class="alternative-btn" bindtap="onScanDocument">
      <view class="btn-left">
        <text class="btn-text">扫描行驶证/vin码</text>
        <view class="accuracy-tag">
          <text class="tag-text">车型信息更准确</text>
        </view>
      </view>
      <image src="https://oss.csdu.net/ztl/images/scan.png" class="scan-icon"></image>
    </view>
  </view>

  <!-- 省份选择器弹窗 -->
  <view class="picker-overlay" wx:if="{{showProvincePicker}}" bindtap="closeProvincePicker">
    <view class="picker-content" catchtap="stopPropagation">
      <view class="picker-header">
        <text class="picker-title">选择省份</text>
        <view class="picker-close" bindtap="closeProvincePicker">×</view>
      </view>
      <view class="province-grid">
        <view class="province-item" wx:for="{{provinces}}" wx:key="index" 
              bindtap="selectProvince" data-province="{{item}}">
          <text class="province-text">{{item}}</text>
        </view>
      </view>
    </view>
  </view>



  <!-- 历史记录弹窗 -->
  <view class="picker-overlay" wx:if="{{showHistoryList}}" bindtap="hideHistory">
    <view class="picker-content" catchtap="stopPropagation">
      <view class="picker-header">
        <text class="picker-title">历史车牌</text>
        <view class="picker-close" bindtap="hideHistory">×</view>
      </view>
      <view class="history-list">
        <view class="history-item" wx:for="{{historyPlates}}" wx:key="index" 
              bindtap="selectHistoryPlate" data-plate="{{item}}">
          <text class="history-text">{{item}}</text>
        </view>
        <view class="history-empty" wx:if="{{historyPlates.length === 0}}">
          <text class="empty-text">暂无历史记录</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 自定义键盘弹窗 -->
  <view class="keyboard-overlay {{showKeyboard ? 'show' : ''}}" bindtap="hideKeyboard">
    <view class="keyboard-container" catchtap="stopPropagation">
      <view class="keyboard-header">
        <view class="keyboard-title">请输入车牌号</view>
        <view class="keyboard-close" bindtap="hideKeyboard">
          <text>×</text>
        </view>
      </view>

      <!-- 当前输入显示 -->
      <view class="current-plate">
        <view wx:for="{{plateArray}}" wx:key="index" 
              class="current-item {{currentInputIndex === index ? 'current-active' : ''}}"
              data-index="{{index}}" 
              bindtap="onInputTap">
          <text wx:if="{{item}}">{{item}}</text>
          <text wx:else class="current-placeholder">{{index === 0 ? '省' : index === 1 ? '市' : index === 7 ? '新能源' : ''}}</text>
        </view>
      </view>

      <!-- 键盘区域 -->
      <view class="keyboard-body">
        <!-- 省份键盘 -->
        <view wx:if="{{currentInputIndex === 0}}" class="key-container">
          <view wx:for="{{provinces}}" wx:for-item="row" wx:for-index="rowIndex" wx:key="rowIndex" class="key-row">
            <view wx:for="{{row}}" wx:for-item="key" wx:key="index" 
                  class="key-item {{!key ? 'disabled' : ''}}" 
                  data-key="{{key}}" 
                  bindtap="clickKey">
              <text>{{key}}</text>
            </view>
          </view>
        </view>

        <!-- 数字字母键盘 -->
        <view wx:else class="key-container">
          <view wx:for="{{letterNumberKeys}}" wx:for-item="row" wx:for-index="rowIndex" wx:key="rowIndex" class="key-row">
            <view wx:for="{{row}}" wx:for-item="key" wx:key="index" 
                  class="key-item {{!key ? 'disabled' : ''}}" 
                  data-key="{{key}}" 
                  bindtap="clickKey">
              <text>{{key}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-row">
          <view class="action-btn delete-btn" bindtap="deleteKey">
            <text>删除</text>
          </view>
          <view class="action-btn confirm-btn" bindtap="confirmKeyboard">
            <text>确认</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 