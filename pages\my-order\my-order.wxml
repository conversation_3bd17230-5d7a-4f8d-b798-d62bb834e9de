<view class="page">
  <!-- 顶部导航栏 -->
  <view class="header" style="padding-top: {{statusBarHeight}}px;">
    <view class="header-content">
      <image class="back-icon" src="https://oss.csdu.net/ztl/images/arrow_left.png" bindtap="onBack"></image>
      <image class="more-icon" src="/icon/sy.png"></image>
      <text class="header-title">我的订单</text>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tab-container">
    <view class="tab-list">
      <view 
        wx:for="{{tabs}}" 
        wx:key="index"
        class="tab-item {{currentTab === index ? 'active' : ''}}"
        bindtap="onTabChange"
        data-index="{{index}}"
        id="tab-{{index}}"
      >
        <text class="tab-text">{{item}}</text>
      </view>
    </view>
    <view class="tab-indicator" style="left: {{indicatorLeft}}px"></view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view 
      wx:for="{{orderList}}" 
      wx:key="id"
      class="order-item"
    >
      <!-- 订单头部 -->
      <view class="order-header">
        <view class="shop-info">
          <image class="shop-icon" src="https://oss.csdu.net/ztl/images/mendian1.png"></image>
          <text class="shop-name">{{item.shopName}}</text>
        </view>
        <text class="order-status" style="color: {{item.statusColor}}">{{item.status}}</text>
        <image class="status-icon" src="https://oss.csdu.net/ztl/images/shuxian.png"></image>
        <image class="arrow-icon" src="https://oss.csdu.net/ztl/images/shanchu.png"></image>
      </view>

      <!-- 服务信息 -->
      <view class="service-info">
        <view class="service-image"></view>
        <view class="service-details">
          <text class="service-name">{{item.serviceName}}</text>
          <text class="service-desc">{{item.serviceDesc}}</text>
          <view class="price-container">
            <text class="price-symbol">￥</text>
            <text class="price-value">{{item.price}}</text>
          </view>
        </view>
      </view>

      <!-- 订单金额 -->
      <view class="order-amount">
        <text class="amount-text">共1件  {{item.orderType}}</text>
        <text class="amount-value">￥{{item.totalPriceFormatted}}</text>
      </view>

      <!-- 预约时间（如果有） -->
      <view wx:if="{{item.appointmentTime}}" class="appointment-time">
        <image class="time-icon" src="https://oss.csdu.net/ztl/images/red_time.png"></image>
        <text class="time-label">预约时间  </text>
        <text class="time-value">{{item.appointmentTime}}</text>
      </view>

      <!-- 分割线 -->
      <view wx:if="{{item.status === '待评价'}}" class="divider"></view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <!-- 待服务状态 -->
        <block wx:if="{{item.status === '待服务'}}">
          <view class="btn btn-outline" bindtap="onApplyInvoice" data-id="{{item.id}}">
            <text class="btn-text">申请开票</text>
          </view>
          <view class="btn btn-outline" bindtap="onModifyAppointment" data-id="{{item.id}}">
            <text class="btn-text">修改预约</text>
          </view>
        </block>

        <!-- 待评价状态 -->
        <block wx:elif="{{item.status === '待评价'}}">
          <view class="btn btn-outline" bindtap="onViewInvoice" data-id="{{item.id}}">
            <text class="btn-text">查看发票</text>
          </view>
          <view class="btn btn-outline" bindtap="onRateNow" data-id="{{item.id}}">
            <text class="btn-text">立即评价</text>
          </view>
        </block>

        <!-- 已取消状态 -->
        <block wx:elif="{{item.status === '已取消'}}">
          <view class="btn btn-primary" bindtap="onOrderAgain" data-id="{{item.id}}">
            <text class="btn-text">再来一单</text>
          </view>
        </block>

        <!-- 待付款状态 -->
        <block wx:elif="{{item.status === '待付款'}}">
          <view class="btn btn-primary" bindtap="onPayNow" data-id="{{item.id}}">
            <text class="btn-text">立即支付</text>
          </view>
        </block>

        <!-- 待服务状态（有预约时间） -->
        <block wx:elif="{{item.appointmentTime}}">
          <view class="btn btn-outline" bindtap="onApplyInvoice" data-id="{{item.id}}">
            <text class="btn-text">申请开票</text>
          </view>
          <view class="btn btn-primary" bindtap="onAppointmentToShop" data-id="{{item.id}}">
            <text class="btn-text">预约到店</text>
          </view>
        </block>
      </view>
    </view>
  </view>
  
  <!-- 底部提示 -->
  <view class="bottom-tip">
    <view class="divider-line"></view>
    <text class="no-more-text">没有更多</text>
  </view>
</view> 