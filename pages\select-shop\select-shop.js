Page({
  data: {
    statusBarHeight: 44, // 默认值，动态获取
    selectedShopIndex: 0, // 当前选中的门店索引
    shops: [
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: true
      },
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: false
      },
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: false
      },
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: false
      },
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: false
      },
      {
        name: "美车堂COXOPARK旗舰店",
        rating: "4.99",
        businessHours: "10:00-21:00",
        distance: "10.5km",
        address: "福田区福华三路269号福田星河COCO Park...",
        selected: false
      }
    ]
  },

  onLoad: function(options) {
    const statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
    this.setData({ statusBarHeight });
    console.log('门店选择页面加载成功');
    
    // 如果有传入的门店索引，设置选中状态
    if (options.shopIndex) {
      this.setData({
        selectedShopIndex: parseInt(options.shopIndex)
      });
    }
  },

  onShow: function() {
    console.log('门店选择页面显示');
  },

  // 选择门店
  selectShop: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('选择门店:', index);
    
    // 更新所有门店的选中状态
    const shops = this.data.shops.map((shop, i) => ({
      ...shop,
      selected: i === index
    }));
    
    this.setData({
      shops: shops,
      selectedShopIndex: index
    });

    // 显示选择成功提示
    wx.showToast({
      title: '门店选择成功',
      icon: 'success',
      duration: 1500
    });

    // 延迟返回上一页或跳转到指定页面
    setTimeout(() => {
      // 获取页面栈
      const pages = getCurrentPages();
      if (pages.length > 1) {
        // 返回上一页并传递选中的门店信息
        const prevPage = pages[pages.length - 2];
        if (prevPage && prevPage.setSelectedShop) {
          prevPage.setSelectedShop(this.data.shops[index]);
        }
        wx.navigateBack();
      } else {
        // 如果没有上一页，跳转到首页
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }
    }, 1500);
  },

  // 搜索门店
  onSearchTap: function() {
    console.log('点击搜索');
    wx.showToast({
      title: '搜索功能开发中',
      icon: 'none',
      duration: 2000
    });
  },

  // 返回上一页
  onBackTap: function() {
    wx.navigateBack();
  }
}); 