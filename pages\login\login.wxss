/* miniprogram/pages/login/login.wxss */
.container {
    min-height: 100vh;
    background-color: white;
    display: flex;
    flex-direction: column;
}

/* Logo区域 */
.logo-container {
    padding: 80rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo {
    width: 160rpx;
    height: 160rpx;
    background-color: #9c27b0;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 48rpx;
    font-weight: 500;
    margin-bottom: 30rpx;
}

.logo-text {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
}

/* 登录内容区域 */
.content {
    flex: 1;
    padding: 40rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.login-section {
    width: 100%;
}

.login-title {
    font-size: 48rpx;
    font-weight: 600;
    margin-bottom: 60rpx;
    text-align: center;
    color: #333;
}

/* 登录按钮 */
.login-btn {
    width: 100% !important;
    height: 100rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 30rpx;
    border: none;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.login-btn::after {
    border: none;
}

.wechat-btn {
    background-color: #07c160;
    color: white;
}

.login-btn:disabled {
    opacity: 0.6;
}

.login-btn:not(:disabled):active {
    transform: scale(0.98);
    transition: transform 0.1s;
}

/* 用户协议区域 */
.agreement-section {
    margin-top: 30rpx;
}

.agreement {
    display: flex;
    align-items: flex-start;
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
}

.checkbox {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #ddd;
    border-radius: 8rpx;
    margin-right: 16rpx;
    margin-top: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    flex-shrink: 0;
}

.checkbox.checked {
    background-color: #9c27b0;
    border-color: #9c27b0;
}

.check-icon {
    color: white;
    font-size: 20rpx;
    font-weight: bold;
    line-height: 1;
}

.agreement-text {
    flex: 1;
}

.policy-link {
    color: #9c27b0;
    text-decoration: underline;
}

/* 底部 */
.footer {
    padding: 40rpx;
    text-align: center;
    font-size: 24rpx;
    color: #999;
}

/* 加载状态 */
.login-btn[loading] {
    opacity: 0.8;
}

/* 响应式适配 */
@media (max-height: 600px) {
    .logo-container {
        padding: 40rpx 0;
    }
    
    .logo {
        width: 120rpx;
        height: 120rpx;
        font-size: 40rpx;
    }
    
    .login-title {
        font-size: 40rpx;
        margin-bottom: 40rpx;
    }
} 