/* pages/shop-detail/shop-detail.wxss */

/* ===== 自定义导航栏 ===== */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 88rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 24rpx;
  flex: 1;
  position: relative;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  left: 24rpx;
  z-index: 10;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
}

.home-icon {
  width: 32rpx;
  height: 32rpx;
}

.navbar-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 31rpx;
  font-weight: 500;
  color: #252027;
  line-height: 31rpx;
  z-index: 5;
}

.navbar-right {
  position: absolute;
  right: 24rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.phone-number {
  font-size: 28rpx;
  color: #252027;
  font-weight: 500;
}

.navbar-spacer {
  width: 750rpx;
  background: #f4f4f4;
  display: block;
  position: relative;
  left: 0;
  margin: 0;
  padding: 0;
}

/* ===== 页面主体 ===== */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f4f4f4;
}

/* ===== 轮播图 ===== */
.banner-swiper {
  width: 750rpx;
  height: 440rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* ===== 门店信息卡片 ===== */
.shop-info-card {
  background-color: #fff;
  border-radius: 25rpx;
  margin: -61rpx 24rpx 0 23rpx;
  padding: 30rpx;
  position: relative;
  z-index: 10;
  box-shadow: 0 -4rpx 20rpx rgba(0,0,0,0.1);
}

/* 基本信息区域 */
.basic-info-section {
  background-color: #fff;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #252027;
  margin-bottom: 20rpx;
  display: block;
}

.shop-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.rating-info {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.rating-score {
  font-size: 24rpx;
  color: #e23d5d;
  font-weight: bold;
}

.review-count {
  font-size: 24rpx;
  color: #666;
}

.business-status {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.status-text {
  background-color: #21D4A3;
  color: #fff;
  font-size: 20rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-right: 10rpx;
}

.business-hours {
  background-color: rgba(33, 212, 163, 0.1);
  border-radius: 10rpx;
  padding: 4rpx 10rpx;
}

.hours-text {
  font-size: 18rpx;
  color: #21D4A3;
  font-weight: 500;
}

.shop-address-section {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 20rpx;
  background-color: #ECEBF7;
  border-radius: 15rpx;
  padding: 20rpx;
  height: 168rpx;
}

.address-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.address-text {
  font-size: 28rpx;
  color: #252027;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.distance-text {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.address-actions {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  flex-shrink: 0;
  align-self: flex-end;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 8rpx;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #252027;
  font-weight: 500;
}

/* ===== 服务区域容器 ===== */
.services-container {
  background-color: #fff;
  border-radius: 25rpx;
  margin: 20rpx 23rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

/* ===== 服务分类区域 ===== */
.service-filters {
  width: 100%;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 27rpx 30rpx;
  gap: 9rpx;
}

.service-item {
  width: 156rpx;
  height: 83rpx;
  position: relative;
  z-index: 10;
  pointer-events: auto;
  flex-shrink: 0;
}

.service-image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

/* 分隔线 */
.divider-line {
  height: 1rpx;
  background-color: #EBEBF5;
  margin: 0 30rpx;
}

/* ===== 服务列表区域 ===== */
.services-section {
  padding: 20rpx 30rpx 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-card {
  background-color: #fff;
  border-radius: 25rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.service-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.service-info {
  display: flex;
  flex: 1;
  gap: 20rpx;
}

.service-thumbnail {
  width: 120rpx;
  height: 120rpx;
  border-radius: 15rpx;
  flex-shrink: 0;
}

.service-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #252027;
  margin-bottom: 10rpx;
  line-height: 1.3;
}

.service-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.service-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #e23d5d;
  font-weight: 700;
}

.price-amount {
  font-size: 36rpx;
  color: #e23d5d;
  font-weight: 700;
}

.original-price {
  display: flex;
  flex-direction: row;
  margin-left: 20rpx;
  align-items: baseline;
  gap: 8rpx;
}

.original-label {
  font-size: 20rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.original-amount {
  font-size: 20rpx;
  color: #999;
  text-decoration: line-through;
}

.service-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15rpx;
  justify-content: flex-end;
  margin-right: -20rpx;
}.sold-text {
  font-size: 20rpx;
  color: #999;
  border: 1rpx solid #ECEBF7;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-left: auto;
}

.buy-btn {
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 25rpx;
  width: 128rpx;
  height: 58rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(63, 103, 255, 0.2);
  align-self: flex-end;
  margin-top: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: auto;
}

.buy-btn:active {
  transform: scale(0.98);
}

.buy-text {
  font-size: 28rpx;
  color: #fff;
  font-weight: 600;
  line-height: 58rpx;
}
