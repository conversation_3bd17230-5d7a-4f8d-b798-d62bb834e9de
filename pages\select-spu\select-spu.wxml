<!--pages/select-spu/select-spu.wxml-->
<view class="custom-navbar" style="padding-top:{{statusBarHeightRpx}}rpx;height:{{statusBarHeightRpx+88}}rpx;">
  <view class="navbar-content">
    <view class="navbar-left" bindtap="onBackTap">
      <image src="https://oss.csdu.net/ztl/images/arrow_left.png" class="back-icon"></image>
    </view>
    <view class="navbar-title">选择门店服务</view>
    <view class="navbar-right"></view>
  </view>
</view>
<view class="navbar-spacer" style="height:{{statusBarHeightRpx+88}}rpx;"></view>
<view class="service-filters">
  <view class="box_9">
    <view class="text-wrapper_1">
      <text lines="1" class="text_9">推荐</text>
      <text lines="1" class="text_10">recommend</text>
    </view>
    <image src="https://oss.csdu.net/ztl/images/baoyang_bg.png" class="image_12"></image>
  </view>
  <image src="https://oss.csdu.net/ztl/images/xiche_bg.png" class="service-image"></image>
  <image src="https://oss.csdu.net/ztl/images/meirong_bg.png" class="service-image"></image>
  <view class="image-wrapper_4">
    <image src="https://oss.csdu.net/ztl/images/weixiu_bg.png" class="service-image"></image>
  </view>
</view>
<view class="spu-page" style="margin-top:0;">
  <view class="spu-card">
    <block wx:for="{{products}}" wx:key="id">
      <view class="product-row">
        <image class="product-img" src="{{item.image}}" mode="aspectFill"></image>
        <view class="product-content">
          <view class="product-title">{{item.title}}</view>
          <view class="product-desc">{{item.desc}}</view>
          <view class="product-bottom">
            <view class="product-price">
              <text class="price">￥{{item.price}}</text>
              <text class="old-price">原价￥{{item.oldPrice}}</text>
            </view>
          </view>
        </view>
        <view class="btn-order">{{item.btnText}}</view>
      </view>
      <view class="divider" wx:if="{{index < products.length - 1}}"></view>
    </block>
  </view>
</view>