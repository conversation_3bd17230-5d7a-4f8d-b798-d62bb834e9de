// pages/select-spu/select-spu.js
Page({
  data: {
    statusBarHeight: 44,
    statusBarHeightRpx: 88,
    products: [
      {
        id: 1,
        title: '标准洗车-五座轿车',
        desc: '含车辆外观清洗及内饰擦拭',
        price: 42,
        oldPrice: 68,
        image: '/images/select_spu/13711f572eef64647a88c6159a8838bd.png',
        btnText: '预约'
      },
      {
        id: 2,
        title: '精致洗车-七座轿车',
        desc: '对车身表面顽固污垢进行有效清洗',
        price: 99,
        oldPrice: 129,
        image: '/images/select_spu/13711f572eef64647a88c6159a8838bd.png',
        btnText: '预约'
      }
    ]
  },
  onLoad() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight; // px
    const screenWidth = systemInfo.screenWidth; // px
    // 1px = 750 / screenWidth rpx
    const px2rpx = 750 / screenWidth;
    const statusBarHeightRpx = Math.round(statusBarHeight * px2rpx);
    this.setData({ statusBarHeight, statusBarHeightRpx });
    console.info('页面加载');
  },
  onShow() {},
  onUnload() {
    console.info('页面卸载');
  },
  onBackTap() {
    wx.navigateBack({});
  },
  onProductTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.showToast({ title: '点击了商品' + id, icon: 'none' });
  }
});