Component({
  properties: {
    // 车牌号数据
    plateNumber: {
      type: Object,
      value: {
        province: '',
        letter: '',
        letter2: '',
        letter3: '',
        letter4: '',
        letter5: '',
        letter6: '',
        newEnergy: ''
      }
    },
    // 是否同意协议
    agreed: {
      type: Boolean,
      value: false
    },
    // 是否显示新能源框
    showNewEnergy: {
      type: Boolean,
      value: true
    },

    // 是否显示历史记录
    showHistory: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 省份列表（按行排列）
    provinces: [
      ['湘', '京', '沪', '粤', '津', '冀', '晋', '蒙', '辽', '吉'],
      ['黑', '苏', '浙', '皖', '闽', '赣', '鲁', '豫', '鄂', '桂'],
      ['琼', '川', '贵', '云', '藏', '陕', '甘', '青', '宁', '新'],
      ['警', '学', '领', '试', '超', '挂', '港', '澳', '使', '']
    ],
    // 字母数字键盘（按行排列）
    letterNumberKeys: [
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'],
      ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P'],
      ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ''],
      ['Z', 'X', 'C', 'V', 'B', 'N', 'M', '', '', '']
    ],
    // 字母数字组合
    letters: [
      'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 
      'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'
    ],
    numbers: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
    // 当前输入框索引
    currentInputIndex: 0,
    // 是否显示省份选择器
    showProvincePicker: false,
    // 是否显示自定义键盘
    showKeyboard: false,
    // 车牌数组（用于键盘显示）
    plateArray: ['', '', '', '', '', '', '', ''],

    // 历史车牌记录
    historyPlates: [],
    // 是否显示历史记录
    showHistoryList: false,
    // 车牌格式验证结果
    plateValid: false,
    // 车牌类型
    plateType: 'normal', // 'normal', 'newEnergy', 'military', 'police'
    // 输入框焦点状态
    focusStates: [true, false, false, false, false, false, false, false]
  },

  lifetimes: {
    attached: function() {
      this.loadHistoryPlates();
      this.validatePlate();
      this.updatePlateArray();
    }
  },

  methods: {
    // 输入框点击
    onInputTap: function(e) {
      const index = parseInt(e.currentTarget.dataset.index);
      this.setData({
        currentInputIndex: index,
        focusStates: this.data.focusStates.map((_, i) => i === index),
        showKeyboard: true
      });
      this.updatePlateArray();
    },

    // 显示键盘
    showKeyboard: function() {
      this.setData({
        showKeyboard: true
      });
    },

    // 隐藏键盘
    hideKeyboard: function() {
      this.setData({
        showKeyboard: false
      });
    },

    // 点击键盘按键
    clickKey: function(e) {
      const key = e.currentTarget.dataset.key;
      if (!key) return;

      const { currentInputIndex, plateNumber } = this.data;
      const fieldNames = ['province', 'letter', 'letter2', 'letter3', 'letter4', 'letter5', 'letter6', 'newEnergy'];
      
      // 更新车牌号
      const newPlateNumber = { ...plateNumber };
      newPlateNumber[fieldNames[currentInputIndex]] = key;
      
      let newCurrentIndex = currentInputIndex;
      if (currentInputIndex < 7) {
        newCurrentIndex = currentInputIndex + 1;
      }

      this.setData({
        plateNumber: newPlateNumber,
        currentInputIndex: newCurrentIndex,
        focusStates: this.data.focusStates.map((_, i) => i === newCurrentIndex)
      });
      
      this.updatePlateArray();
      this.validatePlate();
      this.triggerEvent('platechange', { plateNumber: newPlateNumber });
    },

    // 删除按键
    deleteKey: function() {
      const { currentInputIndex, plateNumber } = this.data;
      const fieldNames = ['province', 'letter', 'letter2', 'letter3', 'letter4', 'letter5', 'letter6', 'newEnergy'];
      
      const newPlateNumber = { ...plateNumber };
      
      if (newPlateNumber[fieldNames[currentInputIndex]]) {
        newPlateNumber[fieldNames[currentInputIndex]] = '';
      } else if (currentInputIndex > 0) {
        newPlateNumber[fieldNames[currentInputIndex - 1]] = '';
        this.setData({
          currentInputIndex: currentInputIndex - 1,
          focusStates: this.data.focusStates.map((_, i) => i === currentInputIndex - 1)
        });
      }

      this.setData({
        plateNumber: newPlateNumber
      });
      
      this.updatePlateArray();
      this.validatePlate();
      this.triggerEvent('platechange', { plateNumber: newPlateNumber });
    },

    // 确认键盘输入
    confirmKeyboard: function() {
      this.hideKeyboard();
    },

    // 更新车牌数组（用于键盘显示）
    updatePlateArray: function() {
      const { plateNumber } = this.data;
      const plateArray = [
        plateNumber.province,
        plateNumber.letter,
        plateNumber.letter2,
        plateNumber.letter3,
        plateNumber.letter4,
        plateNumber.letter5,
        plateNumber.letter6,
        plateNumber.newEnergy // 新能源框内容
      ];
      this.setData({ plateArray });
      // 确保验证车牌类型
      this.validatePlate();
    },

    // 省份选择（保留用于历史记录选择）
    onProvinceTap: function() {
      this.setData({
        showProvincePicker: true
      });
    },

    // 选择省份
    selectProvince: function(e) {
      const province = e.currentTarget.dataset.province;
      this.setData({
        'plateNumber.province': province,
        showProvincePicker: false,
        currentInputIndex: 1,
        focusStates: [false, true, false, false, false, false, false, false]
      });
      this.validatePlate();
      this.triggerEvent('provincechange', { province });
    },

    // 关闭省份选择器
    closeProvincePicker: function() {
      this.setData({
        showProvincePicker: false
      });
    },





    // 页面点击处理
    onPageTap: function(e) {
      // 检查是否点击了输入框区域
      const target = e.target;
      if (target && target.dataset && target.dataset.index !== undefined) {
        // 点击了输入框，不处理
        return;
      }
      
      // 检查是否点击了输入框的父元素
      if (target && target.className && target.className.includes('plate-input')) {
        // 点击了输入框，不处理
        return;
      }
      
      // 点击了其他区域，清除所有焦点状态
      this.setData({
        focusStates: this.data.focusStates.map(() => false)
      });
    },



    // 车牌格式验证
    validatePlate: function() {
      const { plateNumber } = this.data;
      const fullPlate = plateNumber.province + plateNumber.letter + plateNumber.letter2 + 
                       plateNumber.letter3 + plateNumber.letter4 + plateNumber.letter5 + plateNumber.letter6;
      
      // 基础验证：长度和格式
      let valid = false;
      let type = 'normal';
      
      if (fullPlate.length === 7) {
        // 普通车牌验证
        const province = plateNumber.province;
        const letter = plateNumber.letter;
        const rest = plateNumber.letter2 + plateNumber.letter3 + plateNumber.letter4 + plateNumber.letter5 + plateNumber.letter6;
        
        // 省份验证
        const allProvinces = this.data.provinces.flat().filter(p => p);
        if (allProvinces.includes(province)) {
          // 字母验证
          if (this.data.letters.includes(letter)) {
            // 后续字符验证（字母或数字）
            const restValid = rest.split('').every(char => 
              this.data.letters.includes(char) || this.data.numbers.includes(char)
            );
            
            if (restValid) {
              valid = true;
            }
          }
        }
      }
      
      this.setData({
        plateValid: valid,
        plateType: type
      });
      
      return valid;
    },



    // 保存车牌到历史记录
    saveToHistory: function(plateNumber) {
      const fullPlate = plateNumber.province + plateNumber.letter + plateNumber.letter2 + 
                       plateNumber.letter3 + plateNumber.letter4 + plateNumber.letter5 + plateNumber.letter6;
      
      let history = wx.getStorageSync('plateHistory') || [];
      
      // 移除重复项
      history = history.filter(item => item !== fullPlate);
      
      // 添加到开头
      history.unshift(fullPlate);
      
      // 限制数量
      if (history.length > 10) {
        history = history.slice(0, 10);
      }
      
      wx.setStorageSync('plateHistory', history);
      this.setData({
        historyPlates: history
      });
    },

    // 加载历史记录
    loadHistoryPlates: function() {
      const history = wx.getStorageSync('plateHistory') || [];
      this.setData({
        historyPlates: history
      });
    },

    // 显示历史记录
    showHistory: function() {
      this.setData({
        showHistoryList: true
      });
    },

    // 隐藏历史记录
    hideHistory: function() {
      this.setData({
        showHistoryList: false
      });
    },

    // 选择历史车牌
    selectHistoryPlate: function(e) {
      const plate = e.currentTarget.dataset.plate;
      this.parsePlateString(plate);
      this.hideHistory();
    },

    // 解析车牌字符串
    parsePlateString: function(plateString) {
      if (plateString.length === 7) {
        const plateNumber = {
          province: plateString[0],
          letter: plateString[1],
          letter2: plateString[2],
          letter3: plateString[3],
          letter4: plateString[4],
          letter5: plateString[5],
          letter6: plateString[6],
          newEnergy: ''
        };
        
        this.setData({
          plateNumber: plateNumber
        });
        
        this.validatePlate();
        this.triggerEvent('platechange', { plateNumber });
      }
    },



    // 切换用户协议
    onAgreementToggle: function() {
      this.triggerEvent('agreementtoggle');
    },

    // 用户协议链接
    onAgreementLink: function() {
      this.triggerEvent('agreementlink');
    },

    // 提交绑定
    onSubmit: function() {
      if (!this.data.plateValid) {
        wx.showToast({
          title: '请输入正确的车牌号',
          icon: 'none'
        });
        return;
      }
      
      // 保存到历史记录
      this.saveToHistory(this.data.plateNumber);
      
      this.triggerEvent('submit', {
        plateNumber: this.data.plateNumber,
        plateType: this.data.plateType
      });
    },

    // 手动选择车型
    onManualSelect: function() {
      this.triggerEvent('manualselect');
    },

    // 扫描证件
    onScanDocument: function() {
      this.triggerEvent('scandocument');
    },

    // 阻止事件冒泡
    stopPropagation: function(e) {
      // 阻止事件冒泡
    },


  }
}); 