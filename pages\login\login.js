import request from '../../utils/request.js'
const auth = require('../../utils/auth.js')
import { navigateBack } from '../../utils/navigation.js'

Page({
  data: {
    loading: false,
    agreed: false,
    redirectUrl: '' // 登录成功后要跳转的页面
  },

  onLoad(options) {
    // 获取跳转地址
    if (options.redirect) {
      this.setData({
        redirectUrl: decodeURIComponent(options.redirect)
      })
    }
    
    // 检查是否已登录
    this.checkLoginStatus()
  },

  onShow() {
    // 每次显示页面时检查登录状态，避免已登录用户看到登录页
    this.checkLoginStatus()
  },

  // 返回上一页
  navigateBack() {
    navigateBack()
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      const isLoggedIn = auth.isLoggedIn()
      if (isLoggedIn) {
        // 已登录，直接跳转
        this.handleLoginRedirect()
      }
    } catch (error) {
      console.error('检查登录状态失败:', error)
    }
  },

  // 切换协议同意状态
  toggleAgreement() {
    this.setData({
      agreed: !this.data.agreed
    })
  },

  // 处理登录后跳转
  handleLoginRedirect() {
    const redirectUrl = this.data.redirectUrl
    auth.handleLoginRedirect(redirectUrl)
  },

  // 微信登录并获取手机号
  async handleWechatLoginWithPhone(e) {
    if (!this.data.agreed) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none'
      })
      return
    }

    if (!e.detail.code) {
      wx.showToast({
        title: '获取手机号失败，请重试',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 获取微信登录code
      const loginRes = await wx.login()
      const wxCode = loginRes.code
      const phoneCode = e.detail.code
      
      // 调用登录接口
      const result = await request({
        url: '/user/wx/login',
        method: 'POST',
        data: {
          code: wxCode,
          phone_code: phoneCode
        }
      })
      
      if (result.code === 200 && result.data) {
        // 根据实际返回结构处理
        if (typeof result.data === 'string') {
          // 直接返回token字符串的情况
          const token = result.data;
          
          // 先保存token
          auth.setToken(token, 86400);
          
          // 调用用户信息接口获取完整用户信息
          try {
            const userResult = await request({
              url: '/user/profile',
              method: 'GET'
            });
            
            if (userResult.code === 200 && userResult.data) {
              // 保存完整的用户信息
              const userInfo = {
                id: userResult.data.id,
                user_id: userResult.data.id, // 兼容性字段
                nick: userResult.data.nick,
                mobile: userResult.data.mobile,
                avatar: userResult.data.avatar,
                yue: userResult.data.yue,
                open_id: userResult.data.open_id,
                types: userResult.data.types
              };
              
              // 保存用户信息
              auth.setUserInfo(userInfo);
              
              // 处理登录后跳转
              this.handleLoginRedirect()
            } else {
              wx.showToast({
                title: userResult.message || '获取用户信息失败',
                icon: 'none'
              });
            }
          } catch (userError) {
            console.error('调用用户信息接口失败:', userError);
            
            // 降级处理：从token中解析基本信息
            try {
              const { parseJWTToken } = require('../../utils/tokenUtils.js');
              const tokenInfo = parseJWTToken(token);
              
              const userInfo = {
                user_id: tokenInfo.user_id,
                nick: '用户',
                mobile: '',
                avatar: ''
              };
              
              auth.setUserInfo(userInfo);
              
              // 处理登录后跳转
              this.handleLoginRedirect()
            } catch (parseError) {
              console.error('解析token失败:', parseError);
              wx.showToast({
                title: '登录成功，但获取用户信息失败',
                icon: 'none'
              });
            }
          }
        } else if (typeof result.data === 'object') {
          // 返回对象的情况（原有的处理逻辑）
          const { token, user_info } = result.data
          auth.setLoginInfo(token, user_info, 86400)
          this.handleLoginRedirect()
        } else {
          wx.showToast({
            title: '登录返回数据格式错误',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: result.message || '登录失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 同意协议
  onAgreeChange(e) {
    this.setData({
      agreed: e.detail.value
    })
  },

  // 查看用户协议
  viewUserAgreement() {
    wx.showToast({
      title: '用户协议功能暂时不可用',
      icon: 'none'
    })
  },

  // 查看隐私政策
  viewPrivacyPolicy() {
    wx.showToast({
      title: '隐私政策功能暂时不可用',
      icon: 'none'
    })
  }
}) 