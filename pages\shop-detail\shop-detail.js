Page({
  data: {
    statusBarHeight: 44,
    statusBarHeightRpx: 88,
    selectedService: null, // 添加选中的服务类型
    bannerList: [
      {
        image: '/images/shopdetail/banner1.jpg',
        title: '专业汽车服务'
      },
      {
        image: '/images/shopdetail/banner2.jpg',
        title: '品质保证'
      },
      {
        image: '/images/shopdetail/banner3.jpg',
        title: '优惠活动'
      }
    ],
    shopInfo: {
      name: '美车堂COXOPARK旗舰店',
      rating: '4.99',
      reviewCount: 30,
      status: '营业中',
      businessHours: '10:00-21:00',
      address: '福田区福华三路269号福田星河',
      addressDetail: 'COCO Park...',
      distance: '距离您1.1km，驾车6分钟'
    },
    services: [
      {
        name: '标准洗车-五座轿车',
        description: '含车辆外观清洗及内饰擦拭',
        currentPrice: 42,
        originalPrice: 68,
        soldCount: 25
      },
      {
        name: '精致洗车-七座轿车',
        description: '对车身表面顽固污垢进行有效清洗',
        currentPrice: 99,
        originalPrice: 129,
        soldCount: 18
      }
    ]
  },

  onLoad: function (options) {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight; // px
    const screenWidth = systemInfo.screenWidth; // px
    // 1px = 750 / screenWidth rpx
    const px2rpx = 750 / screenWidth;
    const statusBarHeightRpx = Math.round(statusBarHeight * px2rpx);
    this.setData({ statusBarHeight, statusBarHeightRpx });
    
    console.info("商店详情页面加载");
    // 可以从options中获取商店ID等参数
    if (options.shopId) {
      this.loadShopDetail(options.shopId);
    }
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  onUnload: function () {
    console.info("商店详情页面卸载");
  },

  // 加载商店详情数据
  loadShopDetail: function(shopId) {
    // 这里可以调用API获取商店详情数据
    wx.showLoading({
      title: '加载中...'
    });
    
    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      // 更新数据
      this.setData({
        // shopInfo: response.data.shopInfo,
        // services: response.data.services
      });
    }, 1000);
  },

  // 返回上一页
  onBackTap: function() {
    // 尝试返回上一页，如果没有上一页则跳转到首页
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },

  // 首页按钮点击事件
  onHomeTap: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 更多操作
  onMore: function() {
    wx.showActionSheet({
      itemList: ['分享', '收藏', '举报'],
      success: (res) => {
        switch(res.tapIndex) {
          case 0:
            this.shareShop();
            break;
          case 1:
            this.favoriteShop();
            break;
          case 2:
            this.reportShop();
            break;
        }
      }
    });
  },

  // 服务分类点击
  onServiceTap: function(e) {
    const id = e.currentTarget.dataset.id;
    console.log('选择了服务类型:', id);
    
    // 更新选中的服务类型
    this.setData({
      selectedService: this.data.selectedService === id ? null : id
    });
    
    // 根据服务类型筛选服务列表
    this.filterServicesByType(id);
  },

  // 根据服务类型筛选服务
  filterServicesByType: function(serviceType) {
    // 这里可以根据服务类型筛选服务列表
    // 暂时显示提示信息
    const serviceNames = {
      'baoyang': '保养',
      'xiche': '洗车',
      'meirong': '美容',
      'weixiu': '维修'
    };
    
    const serviceName = serviceNames[serviceType] || serviceType;
    wx.showToast({
      title: `选择了${serviceName}服务`,
      icon: 'success'
    });
  },

  // 购买服务
  onBuyService: function(e) {
    const index = e.currentTarget.dataset.index;
    const service = this.data.services[index];
    
    wx.showModal({
      title: '确认购买',
      content: `确定要购买"${service.name}"吗？价格：￥${service.currentPrice}`,
      success: (res) => {
        if (res.confirm) {
          this.buyService(service);
        }
      }
    });
  },

  // 执行购买逻辑
  buyService: function(service) {
    wx.showLoading({
      title: '处理中...'
    });
    
    // 模拟购买API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '购买成功',
        icon: 'success'
      });
      
      // 跳转到订单页面或支付页面
      wx.navigateTo({
        url: '/pages/wait-pay/wait-pay'
      });
    }, 1500);
  },

  // 导航到商店
  onNavigate: function() {
    wx.showToast({
      title: '打开导航',
      icon: 'success'
    });
    // 这里可以调用地图导航API
  },

  // 拨打电话
  onCall: function() {
    wx.makePhoneCall({
      phoneNumber: '************' // 替换为实际电话号码
    });
  },

  // 分享商店
  shareShop: function() {
    wx.showToast({
      title: '分享功能',
      icon: 'success'
    });
  },

  // 收藏商店
  favoriteShop: function() {
    wx.showToast({
      title: '已收藏',
      icon: 'success'
    });
  },

  // 举报商店
  reportShop: function() {
    wx.showToast({
      title: '举报已提交',
      icon: 'success'
    });
  },

  // 页面分享
  onShareAppMessage: function () {
    return {
      title: this.data.shopInfo.name,
      path: '/pages/shop-detail/shop-detail?shopId=' + this.data.shopInfo.id
    };
  }
});
