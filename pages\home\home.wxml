<view class="page" style="padding-top:{{statusBarHeight}}px;">
  <view class="section_1">
    <view class="box_1">
      <view class="box_2">
        <text lines="1" class="text_1">紫太狼养车</text>
        <view class="image-wrapper_1">
          <image src="https://oss.csdu.net/ztl/images/logo.png" class="image_1"></image>
        </view>
      </view>
      <view class="box_3">
        <view class="image-text_1">
          <image src="https://oss.csdu.net/ztl/images/dingwei.png" class="image_3"></image>
          <text lines="1" class="text-group_1" title="深圳市">深圳市</text>
        </view>
        <image src="https://oss.csdu.net/ztl/images/fenge.png" class="image_4"></image>
        <text lines="1" class="text_2" title="洗车/贴膜/打蜡">洗车/贴膜/打蜡</text>
      </view>
    </view>
    <view class="box_15">
      <!-- 未登录状态 -->
      <view wx:if="{{!isLoggedIn}}">
        <view class="text-wrapper_8">
          <text lines="1" decode="true" class="text_22">Hi~添加爱车&nbsp;享受服务</text>
        </view>
        <view class="box_16">
          <view class="list_3">
            <view class="image-text_8-0">
              <image src="https://oss.csdu.net/ztl/images/ckbg.png" class="label_4-0"></image>
              <text lines="1" class="text-group_8-0">车况报告</text>
            </view>
            <view class="image-text_8-1">
              <image src="https://oss.csdu.net/ztl/images/byjl.png" class="label_5-1"></image>
              <text lines="1" class="text-group_8-1">保养记录</text>
            </view>
            <view class="image-text_8-2">
              <image src="https://oss.csdu.net/ztl/images/ksjy.png" class="label_4-2"></image>
              <text lines="1" class="text-group_8-2">快速救援</text>
            </view>
          </view>
          <view class="text-wrapper_9" bindtap="handleAddVehicle">
            <text lines="1" class="text_23">添加爱车</text>
          </view>
        </view>
      </view>
      
      <!-- 已登录状态 -->
      <view wx:else>
        <view class="vehicle-info-container">
          <view class="vehicle-header">
            <view class="vehicle-title">
              <text class="vehicle-brand-model">{{vehicles[0].brand}} {{vehicles[0].model}}</text>
              <image src="https://oss.csdu.net/ztl/images/arrow_down_black.png" class="dropdown-icon"></image>
            </view>
            <view class="plate-number-tag" wx:if="{{vehicles[0].plate_number}}">
              <text class="plate-prefix" style="color: #FFFFFF; font-size: 24rpx; font-weight: bold;">{{vehicles[0].plate_prefix}}</text>
              <text class="plate-number" style="color: #39166C; font-size: 24rpx;">{{vehicles[0].plate_suffix}}</text>
            </view>
          </view>
          
          <view class="vehicle-stats">
            <view class="stat-item">
              <text class="stat-number">{{vehicles[0].maintenance_records}}</text>
              <text class="stat-label">保养记录</text>
            </view>
            <view class="stat-item">
              <text class="stat-number">{{vehicles[0].coupons}}</text>
              <text class="stat-label">卡券</text>
            </view>
          </view>
          
          <view class="vehicle-icon">
            <image src="https://oss.csdu.net/ztl/images/che_bg_white.png" class="car-icon"></image>
          </view>
        </view>
      </view>
    </view>
    <view class="box_4">
      <view class="list_1">
        <view class="image-text_2-0" data-value="1" bindtap="handleServiceCategoryTap">
          <image src="https://oss.csdu.net/ztl/images/baoyang.png" class="image_5-0"></image>
          <text lines="1" class="text-group_2-0">保养</text>
        </view>
        <view class="image-text_2-1" data-value="2" bindtap="handleServiceCategoryTap">
          <image src="https://oss.csdu.net/ztl/images/xihu.png" class="image_5-1"></image>
          <text lines="1" class="text-group_2-1">洗护</text>
        </view>
        <view class="image-text_2-2" data-value="3" bindtap="handleServiceCategoryTap">
          <image src="https://oss.csdu.net/ztl/images/weixiu.png" class="image_5-2"></image>
          <text lines="1" class="text-group_2-2">维修</text>
        </view>
        <view class="image-text_2-3" data-value="4" bindtap="handleServiceCategoryTap">
          <image src="https://oss.csdu.net/ztl/images/meirong.png" class="image_5-3"></image>
          <text lines="1" class="text-group_2-3">美容</text>
        </view>
      </view>
      <view class="box_5">
        <view class="box_6">
          <text lines="1" class="text_3">会员专享</text>
          <text lines="1" class="text_4">更多优惠</text>
          <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="thumbnail_1"></image>
        </view>
        <view class="image-wrapper_2">
          <image src="https://oss.csdu.net/ztl/images/ad1.png" class="image_6"></image>
          <image src="https://oss.csdu.net/ztl/images/ad2.png" class="image_7"></image>
          <image src="https://oss.csdu.net/ztl/images/ad3.png" class="image_8"></image>
        </view>
        <view class="box_7">
          <text lines="1" class="text_5">1线上选服务</text>
          <image src="https://oss.csdu.net/ztl/images/double_arrow_right.png" class="thumbnail_2"></image>
          <text lines="1" decode="true" class="text_6">2&nbsp;选择门店</text>
          <image src="https://oss.csdu.net/ztl/images/double_arrow_right.png" class="thumbnail_3"></image>
          <text lines="1" decode="true" class="text_7">3&nbsp;下单支付</text>
          <image src="https://oss.csdu.net/ztl/images/double_arrow_right.png" class="thumbnail_4"></image>
          <text lines="1" decode="true" class="text_8">4&nbsp;预约到店</text>
        </view>
      </view>
      <view class="image-wrapper_3">
        <image src="https://oss.csdu.net/ztl/images/ad2_1.png" class="image_9"></image>
        <image src="https://oss.csdu.net/ztl/images/ad2_2.png" class="image_10"></image>
      </view>
      <view class="box_8">
        <scroll-view class="service-scroll" scroll-x="true" show-scrollbar="false" enhanced="true">
          <view class="service-container">
            <view class="box_9" bindtap="onServiceTap" data-id="tuijian">
              <view class="text-wrapper_1">
                <text lines="1" class="text_9">推荐</text>
                <text lines="1" class="text_10">recommend</text>
              </view>
              <image wx:if="{{selectedService === 'tuijian'}}" src="https://oss.csdu.net/ztl/images/tuijian_select.png" class="image_12"></image>
              <image wx:else src="https://oss.csdu.net/ztl/images/tuijian_bg.png" class="image_12"></image>
            </view>
            <view class="service-item" bindtap="onServiceTap" data-id="1">
              <image wx:if="{{selectedService === '1'}}" src="https://oss.csdu.net/ztl/images/baoyang_select.png" class="service-image"></image>
              <image wx:else src="https://oss.csdu.net/ztl/images/baoyang_bg.png" class="service-image"></image>
            </view>
            <view class="service-item" bindtap="onServiceTap" data-id="2">
              <image wx:if="{{selectedService === '2'}}" src="https://oss.csdu.net/ztl/images/xiche_select.png" class="service-image"></image>
              <image wx:else src="https://oss.csdu.net/ztl/images/xiche_bg.png" class="service-image"></image>
            </view>
            <view class="service-item" bindtap="onServiceTap" data-id="3">
              <image wx:if="{{selectedService === '3'}}" src="https://oss.csdu.net/ztl/images/weixiu_select.png" class="service-image"></image>
              <image wx:else src="https://oss.csdu.net/ztl/images/weixiu_bg.png" class="service-image"></image>
            </view>
            <view class="service-item" bindtap="onServiceTap" data-id="4">
              <image wx:if="{{selectedService === '4'}}" src="https://oss.csdu.net/ztl/images/meiron_select.png" class="service-image"></image>
              <image wx:else src="https://oss.csdu.net/ztl/images/meirong_bg.png" class="service-image"></image>
            </view>
          </view>
        </scroll-view>
      </view>

      <view class="list_2">
        <scroll-view class="products-scroll-container" scroll-y="true" show-scrollbar="false" enhanced="true">
          <view class="list-items" wx:for="{{products}}" wx:key="id">
            <view class="product-box">
              <image src="{{item.image}}" class="product-image"></image>
              <view class="product-tag">
                <text lines="1" class="product-tag-text">{{item.tag}}</text>
              </view>
            </view>
            <view class="product-info">
              <text lines="1" class="product-title">{{item.title}}</text>
              <view class="product-tags">
                <view class="tag-wrapper">
                  <text lines="1" class="tag-text">{{item.tags[0]}}</text>
                </view>
                <view class="tag-wrapper-wide">
                  <text lines="1" class="tag-text-wide">{{item.tags[1]}}</text>
                </view>
              </view>
              <view class="price-wrapper">
                <text lines="1" class="price-symbol">￥</text>
                <text lines="1" class="price-main">{{item.price}}</text>
                <text lines="1" class="price-original">原价￥{{item.originalPrice}}</text>
              </view>
              <view class="member-price-wrapper">
                <view class="member-price-badge">
                  <text lines="1" class="member-price-text">会员价</text>
                </view>
                <view class="save-price-bg">
                  <text lines="1" class="save-price-text">节省￥{{item.savePrice}}</text>
                </view>
              </view>
              <view class="buy-button">
                <text lines="1" class="buy-button-text">抢购</text>
              </view>
            </view>
          </view>
          
          <!-- 底部分割线和提示 -->
          <view class="list-footer">
            <text class="no-more-text">没有更多了</text>
          </view>
        </scroll-view>
      </view>
      <image src="https://oss.csdu.net/ztl/images/buzhidao.png" class="image_18"></image>
    </view>
  </view>
  <!-- 弹窗遮罩和内容 -->
  <view wx:if="{{showPopup}}" class="popup-mask">
    <view class="popup-content">
      <image class="popup-img" src="https://oss.csdu.net/ztl/images/tc1.png" mode="widthFix" bindtap="onPopupImageTap"></image>
      <image class="popup-close" src="https://oss.csdu.net/ztl/images/tc1_cancel.png" mode="widthFix" bindtap="onPopupClose"></image>
    </view>
  </view>
</view>