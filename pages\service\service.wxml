<view class="page" style="padding-top:{{statusBarHeight}}px;">
  <image class="page-bg" src="https://oss.csdu.net/ztl/images/service_bg.png" mode="scaleToFill"></image>

  <!-- 页面标题 -->
  <view class="page-header">
    <text lines="1" class="page-title">爱车服务</text>
  </view>
  
  <!-- 车辆信息区域 -->
  <view class="vehicle-section">
    <!-- 当前车辆标签 -->
    <view class="vehicle-label">
      <image src="https://oss.csdu.net/ztl/images/che_tag_bg.png" class="vehicle-label-bg"></image>
      <text lines="1" class="vehicle-label-text">当前车辆</text>
    </view>
    
    <!-- 车辆主要信息 -->
    <view class="vehicle-main-info">
      <image src="https://oss.csdu.net/ztl/images/vehicle_image.png" class="vehicle-image"></image>
      <view class="vehicle-details">
        <view class="model-container">
          <text lines="1" decode="true" class="model-name">福特&nbsp;蒙迪欧</text>
          <image src="https://oss.csdu.net/ztl/images/edit_tag.png" class="model-tag"></image>
        </view>
        <text lines="1" decode="true" class="model-specs">2025款&nbsp;1.5T&nbsp;Z自动EcoBoost&nbsp;时尚型</text>
      </view>
      <image src="https://oss.csdu.net/ztl/images/vehicle_change.png" class="info-icon"></image>
    </view>
    
    <!-- 车牌信息区域 -->
    <view class="plate-info">
      <view class="plate-display">
        <text lines="1" class="plate-prefix">湘D</text>
        <text lines="1" class="plate-suffix">3654P1</text>
      </view>
      <view class="auth-container">
        <text lines="1" class="auth-status">未认证</text>
      </view>
    </view>
  </view>
  
  <!-- 服务记录标题 -->
  <view class="service-record-header">
    <image src="https://oss.csdu.net/ztl/images/record.png" class="record-icon"></image>
    <text lines="1" class="record-title">服务记录</text>
  </view>
  
  <!-- 服务记录列表 -->
  <view class="service-record-list">
    <!-- 第一条服务记录 -->
    <view class="service-record-item">
      <view class="record-header">
        <view class="record-time">
          <image src="https://oss.csdu.net/ztl/images/vehicle_time.png" class="time-icon"></image>
          <text lines="1" decode="true" class="time-text">2025-07-13&nbsp;13:06:15</text>
        </view>
        <view class="record-status">
          <text lines="1" class="status-text">待服务</text>
        </view>
      </view>
      <view class="service-project">
        <text lines="1" class="project-label">项目</text>
        <text lines="1" class="project-name">标准洗车-五座轿车</text>
      </view>
      <view class="store-info">
        <text lines="1" class="store-label">门店</text>
        <text lines="1" class="store-name">美车堂COXOPARK旗舰店</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="store-image"></image>
      </view>
      <view class="action-buttons">
        <view class="navigate-btn" bindtap="navigateToStore">
          <text lines="1" class="btn-text">导航门店</text>
        </view>
        <view class="book-btn" bindtap="bookService">
          <text lines="1" class="btn-text">预约服务</text>
        </view>
      </view>
    </view>
    
    <!-- 第二条服务记录 -->
    <view class="service-record-item">
      <view class="record-header">
        <view class="record-time">
          <image src="https://oss.csdu.net/ztl/images/vehicle_time.png" class="time-icon"></image>
          <text lines="1" decode="true" class="time-text">2025-07-13&nbsp;13:06:15</text>
        </view>
        <view class="record-status">
          <text lines="1" class="status-text">待服务</text>
        </view>
      </view>
      <view class="service-project">
        <text lines="1" class="project-label">项目</text>
        <text lines="1" class="project-name">标准洗车-五座轿车</text>
      </view>
      <view class="store-info">
        <text lines="1" class="store-label">门店</text>
        <text lines="1" class="store-name">美车堂COXOPARK旗舰店</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="store-image"></image>
      </view>
      <view class="action-buttons">
        <view class="navigate-btn" bindtap="navigateToStore">
          <text lines="1" class="btn-text">导航门店</text>
        </view>
        <view class="progress-btn" bindtap="viewServiceProgress">
          <text lines="1" class="btn-text">查看进度</text>
        </view>
        <view class="modify-btn" bindtap="modifyBooking">
          <text lines="1" class="btn-text">修改预约</text>
        </view>
      </view>
    </view>
  </view>
</view>