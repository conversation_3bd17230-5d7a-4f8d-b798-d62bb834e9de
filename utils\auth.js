const TOKEN_KEY = 'accessToken';
const USER_INFO_KEY = 'userInfo';

// 导入统一的token解析工具
const { parseJWTToken, isTokenExpired } = require('./tokenUtils');
// 导入导航工具
import { redirectTo, reLaunch, switchTab } from './navigation.js';
// 导入tokenManager
import tokenManager from './tokenManager.js';

// 检查是否已登录
const isLoggedIn = () => {
  return tokenManager.isLoggedIn();
};

// 获取token
const getToken = () => {
  return tokenManager.getToken();
};

// 保存token
const setToken = (token, expiresIn = 86400) => {
  tokenManager.setToken(token, expiresIn);
};

// 获取用户信息
const getUserInfo = () => {
  return tokenManager.getUserInfo();
};

// 保存用户信息
const setUserInfo = (userInfo) => {
  tokenManager.setUserInfo(userInfo);
};

// 设置登录信息（同时设置token和用户信息）
const setLoginInfo = (token, userInfo, expiresIn = 86400) => {
  tokenManager.setToken(token, expiresIn);
  tokenManager.setUserInfo(userInfo);
};

// 清除登录状态
const clearLoginState = () => {
  tokenManager.clearLoginState();
};

// 解析跳转URL，判断是否为tabBar页面
const parseRedirectUrl = (url) => {
  try {
    const decodedUrl = decodeURIComponent(url);
    
    // 检查是否是tabBar页面
    const tabBarPages = [
      '/pages/home/<USER>',
      '/pages/mall/mall',
      '/pages/service/service',
      '/pages/mine/mine'
    ];
    
    const isTabBar = tabBarPages.some(tabPage => decodedUrl.startsWith(tabPage));
    
    return {
      originalUrl: url,
      decodedUrl: decodedUrl,
      urlPath: decodedUrl.split('?')[0],
      isTabBar: isTabBar
    };
  } catch (error) {
    console.error('解析跳转URL失败:', error);
    return {
      originalUrl: url,
      decodedUrl: url,
      urlPath: url.split('?')[0],
      isTabBar: false
    };
  }
};

// 处理登录后跳转（优化版本）
const handleLoginRedirect = (redirectUrl) => {
  if (redirectUrl) {
    // 有跳转地址，解析并跳转
    const urlInfo = parseRedirectUrl(redirectUrl);
    
    try {
      if (urlInfo.isTabBar) {
        // 如果是tabBar页面，使用reLaunch
        reLaunch(urlInfo.decodedUrl);
      } else {
        // 如果是普通页面，使用redirectTo
        redirectTo(urlInfo.decodedUrl);
      }
    } catch (error) {
      console.error('跳转失败:', error);
      // 如果跳转失败，默认跳转到首页
      reLaunch('/pages/home/<USER>');
    }
  } else {
    // 没有跳转地址，默认跳转到首页
    reLaunch('/pages/home/<USER>');
  }
};

// 检查登录状态并跳转到登录页面（新增功能）
const checkLoginAndNavigate = (targetUrl, options = {}) => {
  const { showToast = true, toastMsg = '请先登录' } = options;
  
  if (!isLoggedIn()) {
    if (showToast) {
      wx.showToast({
        title: toastMsg,
        icon: 'none'
      });
    }
    
    // 构建登录页面URL，包含返回地址
    const loginUrl = `/pages/login/login?redirect=${encodeURIComponent(targetUrl)}`;
    
    // 跳转到登录页面
    wx.navigateTo({
      url: loginUrl,
      fail: () => {
        // 如果navigateTo失败，尝试redirectTo
        wx.redirectTo({
          url: loginUrl,
          fail: () => {
            // 最终尝试reLaunch
            wx.reLaunch({
              url: '/pages/login/login'
            });
          }
        });
      }
    });
    return false;
  }
  
  return true;
};

// 验证token有效性并处理过期情况（新增功能）
const validateTokenAndHandle = (options = {}) => {
  const {
    showExpiredToast = true,
    expiredMessage = '登录已过期，请重新登录',
    redirectToLogin = true
  } = options;

  const token = getToken();
  
  // 没有token，直接返回false
  if (!token) {
    if (redirectToLogin) {
      redirectToLoginPage();
    }
    return false;
  }
  
  // 检查token是否过期
  try {
    if (isTokenExpired(token)) {
      // token已过期
      if (showExpiredToast) {
        wx.showToast({
          title: expiredMessage,
          icon: 'none',
          duration: 2000
        });
      }
      
      // 清除过期的登录状态
      clearLoginState();
      
      if (redirectToLogin) {
        // 延迟跳转，让用户看到提示
        setTimeout(() => {
          redirectToLoginPage();
        }, 2000);
      }
      
      return false;
    }
    
    // token有效
    return true;
  } catch (error) {
    console.error('验证token失败:', error);
    // 解析失败，可能token格式有问题，清除登录状态
    clearLoginState();
    
    if (redirectToLogin) {
      redirectToLoginPage();
    }
    
    return false;
  }
};

// 重定向到登录页面（新增功能）
const redirectToLoginPage = () => {
  // 获取当前页面信息
  const pages = getCurrentPages();
  let loginUrl = '/pages/login/login';
  
  if (pages.length > 0) {
    const currentPage = pages[pages.length - 1];
    let returnUrl = `/${currentPage.route}`;
    
    if (currentPage.options && Object.keys(currentPage.options).length > 0) {
      const queryString = Object.keys(currentPage.options)
        .map(key => `${key}=${currentPage.options[key]}`)
        .join('&');
      returnUrl += `?${queryString}`;
    }
    
    loginUrl += `?redirect=${encodeURIComponent(returnUrl)}`;
  }
  
  wx.redirectTo({
    url: loginUrl,
    fail: () => {
      wx.reLaunch({
        url: '/pages/login/login'
      });
    }
  });
};

module.exports = {
  isLoggedIn,
  getToken,
  setToken,
  getUserInfo,
  setUserInfo,
  setLoginInfo,
  clearLoginState,
  handleLoginRedirect,
  checkLoginAndNavigate,
  validateTokenAndHandle,
  redirectToLoginPage
};

/*
使用示例：

1. 在页面中检查登录状态并跳转：
const auth = require('../../utils/auth.js');

// 检查登录状态，如果未登录则跳转到登录页
if (!auth.checkLoginAndNavigate('/pages/mine/mine')) {
  return; // 已跳转到登录页，停止执行
}

2. 在页面加载时验证token：
onLoad() {
  const auth = require('../../utils/auth.js');
  
  // 验证token有效性
  if (!auth.validateTokenAndHandle()) {
    return; // token无效，已处理跳转
  }
  
  // 继续执行页面逻辑
  this.loadData();
}

3. 在需要登录的操作中使用：
async handleOrder() {
  const auth = require('../../utils/auth.js');
  
  // 检查登录状态
  if (!auth.checkLoginAndNavigate('/pages/order/order', {
    showToast: true,
    toastMsg: '请先登录后再下单'
  })) {
    return;
  }
  
  // 继续执行下单逻辑
  this.submitOrder();
}
*/ 