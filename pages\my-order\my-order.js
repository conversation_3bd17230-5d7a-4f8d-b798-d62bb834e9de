Page({
  data: {
    statusBarHeight: 0, // 状态栏高度
    indicatorLeft: 0, // 指示器位置
    // 订单数据
    orderList: [
      {
        id: 1,
        shopName: "美车堂COXOPARK旗舰店",
        status: "待服务",
        statusColor: "#1F263A",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        appointmentTime: "07-14(周一) 17:30",
        orderType: "已付"
      },
      {
        id: 2,
        shopName: "美车堂COXOPARK旗舰店",
        status: "待评价",
        statusColor: "#1F263A",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        orderType: "应付"
      },
      {
        id: 3,
        shopName: "紫太狼商城",
        status: "已取消",
        statusColor: "#999999",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        orderType: "应付"
      },
      {
        id: 4,
        shopName: "美车堂COXOPARK旗舰店",
        status: "已完成",
        statusColor: "#999999",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        orderType: "实付"
      },
      {
        id: 5,
        shopName: "美车堂COXOPARK旗舰店",
        status: "待付款",
        statusColor: "#1F263A",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        orderType: "应付"
      },
      {
        id: 6,
        shopName: "美车堂COXOPARK旗舰店",
        status: "待服务",
        statusColor: "#1F263A",
        serviceName: "标准洗车-五座轿车",
        serviceDesc: "含车辆外观清洗及内饰擦拭",
        price: 42,
        totalPrice: 42.00,
        orderType: "已付",
        appointmentTime: "07-15(周二) 14:30"
      }
    ],
    currentTab: 0, // 当前选中的标签页
    tabs: ["全部", "待付款", "待收货", "待服务", "待评价"]
  },

  onLoad(options) {
    console.info("我的订单页面加载");
    
    // 获取系统信息，设置状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;
    
    // 处理订单数据，格式化金额显示
    const orderList = this.data.orderList.map(order => ({
      ...order,
      totalPriceFormatted: order.totalPrice.toFixed(2)
    }));
    
    // 获取传入的标签页参数
    const tabIndex = options.tab ? parseInt(options.tab) : 0;
    
    // 将状态栏高度存储到全局数据中
    this.setData({
      statusBarHeight: statusBarHeight,
      orderList: orderList,
      currentTab: tabIndex
    });

    // 计算初始指示器位置
    this.calculateIndicatorPosition(tabIndex);
  },

  onShow() {
    console.info("我的订单页面显示");
  },

  onUnload() {
    console.info("我的订单页面卸载");
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    // 模拟刷新数据
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom() {
    console.log('上拉加载更多');
    wx.showToast({
      title: '没有更多数据了',
      icon: 'none'
    });
  },

  // 返回按钮
  onBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },

  // 标签页切换
  onTabChange(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentTab: index
    });
    console.log('切换到标签页:', this.data.tabs[index]);
    
    // 计算指示器位置
    this.calculateIndicatorPosition(index);
  },

  // 计算指示器位置
  calculateIndicatorPosition(tabIndex) {
    const query = wx.createSelectorQuery();
    query.select(`#tab-${tabIndex}`).boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        const tabRect = res[0];
        const containerQuery = wx.createSelectorQuery();
        containerQuery.select('.tab-container').boundingClientRect();
        containerQuery.exec((containerRes) => {
          if (containerRes[0]) {
            const containerRect = containerRes[0];
            // 计算标签页中心相对于容器的位置
            const tabCenter = tabRect.left + tabRect.width / 2;
            const containerLeft = containerRect.left;
            const indicatorLeft = tabCenter - containerLeft;
            
            this.setData({
              indicatorLeft: indicatorLeft
            });
          }
        });
      }
    });
  },





  // 申请开票
  onApplyInvoice(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('申请开票，订单ID:', orderId);
    wx.showToast({
      title: '申请开票功能开发中',
      icon: 'none'
    });
  },

  // 修改预约
  onModifyAppointment(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('修改预约，订单ID:', orderId);
    wx.navigateTo({
      url: '/pages/appointment/appointment'
    });
  },

  // 查看发票
  onViewInvoice(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('查看发票，订单ID:', orderId);
    wx.showToast({
      title: '查看发票功能开发中',
      icon: 'none'
    });
  },

  // 立即评价
  onRateNow(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('立即评价，订单ID:', orderId);
    wx.showToast({
      title: '评价功能开发中',
      icon: 'none'
    });
  },

  // 再来一单
  onOrderAgain(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('再来一单，订单ID:', orderId);
    wx.navigateTo({
      url: '/pages/service/service'
    });
  },

  // 立即支付
  onPayNow(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('立即支付，订单ID:', orderId);
    wx.navigateTo({
      url: '/pages/wait-pay/wait-pay'
    });
  },

  // 预约到店
  onAppointmentToShop(e) {
    const orderId = e.currentTarget.dataset.id;
    console.log('预约到店，订单ID:', orderId);
    wx.navigateTo({
      url: '/pages/appointment/appointment'
    });
  }
}); 