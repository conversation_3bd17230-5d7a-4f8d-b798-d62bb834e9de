Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    storeData: {
      type: Object,
      value: {}
    }
  },
  data: {
    popupClass: '',
    visible: false,
    countdown: '04:05:42', // 倒计时
    storeName: '美车堂COXOPARK旗舰店',
    storeAddress: '福田区福华三路269号福田星河COCO Park...',
    serviceName: '标准洗车-五座轿车',
    currentPrice: '40',
    originalPrice: '128'
  },
  lifetimes: {
    attached: function () {
      // 组件加载
    },
    detached: function () {
      // 组件卸载
    },
  },
  observers: {
    'show': function(newVal) {
      if (newVal) {
        this.setData({ popupClass: 'qianggou-popup-show' });
      } else {
        this.setData({ popupClass: 'qianggou-popup-hide' });
      }
    },
    'storeData': function(newVal) {
      if (newVal && Object.keys(newVal).length > 0) {
        this.setData({
          storeName: newVal.store || '美车堂COXOPARK旗舰店',
          currentPrice: newVal.price || '40',
          originalPrice: newVal.original || '128'
        });
      }
    }
  },
  methods: {
    onMaskTap() {
      this.triggerEvent('close');
    },
    onPayTap() {
      // 点击立即支付
      console.log('qianggou组件 - 立即支付');
      
      // 跳转到待支付页面
      wx.navigateTo({
        url: '/pages/wait-pay/wait-pay'
      });
    },
    onCloseTap() {
      this.triggerEvent('close');
    }
  }
}); 