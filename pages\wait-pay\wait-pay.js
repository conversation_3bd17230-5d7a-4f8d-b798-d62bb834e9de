Page({
  data: {
    statusBarHeight: 44,
    statusBarHeightRpx: 88,
    countdown: {
      hours: 0,
      minutes: 24,
      seconds: 18
    },
    shopInfo: {
      name: '美车堂COXOPARK旗舰店',
      address: '福田区福华三路269号福田星河',
      addressDetail: 'COCO Park B栋一楼'
    },
    orderInfo: {
      serviceName: '标准洗车-五座轿车',
      originalPrice: 98,
      discountAmount: 58,
      finalPrice: 40,
      orderNo: 'TH115035089806331132'
    },
    countdownTimer: null
  },

  onLoad: function (options) {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight; // px
    const screenWidth = systemInfo.screenWidth; // px
    // 1px = 750 / screenWidth rpx
    const px2rpx = 750 / screenWidth;
    const statusBarHeightRpx = Math.round(statusBarHeight * px2rpx);
    this.setData({ statusBarHeight, statusBarHeightRpx });
    
    console.info("待支付页面加载");
    this.startCountdown();
  },

  onUnload: function () {
    console.info("待支付页面卸载");
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  // 开始倒计时
  startCountdown: function() {
    const timer = setInterval(() => {
      let { hours, minutes, seconds } = this.data.countdown;
      
      if (seconds > 0) {
        seconds--;
      } else if (minutes > 0) {
        minutes--;
        seconds = 59;
      } else if (hours > 0) {
        hours--;
        minutes = 59;
        seconds = 59;
      } else {
        // 倒计时结束，自动关闭订单
        clearInterval(timer);
        this.autoCloseOrder();
        return;
      }

      this.setData({
        'countdown.hours': hours,
        'countdown.minutes': minutes,
        'countdown.seconds': seconds
      });
    }, 1000);

    this.setData({
      countdownTimer: timer
    });
  },

  // 自动关闭订单
  autoCloseOrder: function() {
    wx.showModal({
      title: '订单已关闭',
      content: '支付超时，订单已自动关闭',
      showCancel: false,
      success: () => {
        wx.navigateBack();
      }
    });
  },

  // 导航
  onNavigate: function() {
    wx.showToast({
      title: '打开导航',
      icon: 'success'
    });
    // 这里可以调用地图导航API
  },

  // 拨打电话
  onCall: function() {
    wx.makePhoneCall({
      phoneNumber: '************' // 替换为实际电话号码
    });
  },

  // 复制订单号
  onCopyOrderNo: function() {
    wx.setClipboardData({
      data: this.data.orderInfo.orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      }
    });
  },

  // 取消订单
  onCancelOrder: function() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消此订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里调用取消订单的API
          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      }
    });
  },

  // 立即付款
  onPay: function() {
    wx.showLoading({
      title: '正在跳转支付...'
    });
    
    // 模拟支付流程
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }, 2000);
  },

  // 返回按钮点击事件
  onBackTap() {
    wx.navigateBack({});
  },

  // 首页按钮点击事件
  onHomeTap() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  }
});
