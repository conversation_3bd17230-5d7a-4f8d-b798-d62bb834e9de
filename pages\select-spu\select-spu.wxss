/* pages/select-spu/select-spu.wxss */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 88rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
.navbar-content {
  display: flex;
  align-items: baseline;
  height: 44px;
  padding: 0 24rpx;
  flex: 1;
}
.navbar-left {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 25rpx;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
  margin-bottom: 0;
  position: static;
  vertical-align: bottom;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #252027;
  line-height: 44px;
}
.navbar-right {
  width: 60rpx;
}
.navbar-spacer {
  width: 750rpx;
  background: #f5f7fa;
  display: block;
  position: relative;
  left: 0;
  margin: 0;
  padding: 0;
}
.spu-page {
  background: #f5f7fa;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 0 0 32rpx 0;
  /* margin-top 只由wxml内联控制 */
}
.spu-card {
  background: #fff;
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.06);
  padding: 32rpx 0;
  margin-top: 24rpx;
  width: 95vw;
  max-width: 700rpx;
  margin-left: auto;
  margin-right: auto;
}
.product-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 32rpx;
  padding-right: 27rpx;
  min-height: 140rpx;
  margin-bottom: 0;
}
.product-img {
  width: 112rpx;
  height: 112rpx;
  border-radius: 20rpx;
  object-fit: cover;
  margin-right: 24rpx;
  background: #eee;
  flex-shrink: 0;
}
.product-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
  flex: 1;
}
.product-title {
  font-size: 30rpx;
  font-weight: 700;
  color: #121f2b;
  line-height: 1.2;
  word-break: break-all;
}
.product-desc {
  font-size: 24rpx;
  color: #838383;
  margin: 8rpx 0 0 0;
  line-height: 1.2;
  word-break: break-all;
}
.product-bottom {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 12rpx;
}
.product-price {
  display: flex;
  align-items: baseline;
}
.price {
  color: #ff4065;
  font-size: 32rpx;
  font-weight: 700;
  margin-right: 12rpx;
}
.old-price {
  color: #bdbdbd;
  font-size: 22rpx;
  text-decoration: line-through;
}
.btn-order {
  width: 128rpx !important;
  height: 58rpx !important;
  min-width: 128rpx !important;
  min-height: 58rpx !important;
  box-sizing: border-box !important;
  background: linear-gradient(90deg, #3F67FF 0%, #5C299E 100%) !important;
  color: #fff !important;
  font-size: 28rpx !important;
  font-family: PingFang-SC-Bold, PingFang SC, sans-serif !important;
  font-weight: bold !important;
  border-radius: 32rpx !important;
  border: none !important;
  outline: none !important;
  box-shadow: 0 4rpx 12rpx rgba(143,95,255,0.08) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  line-height: 16rpx !important;
  padding: 0 !important;
  margin-left: 32rpx !important;
  margin-right: 0 !important;
  background-clip: padding-box !important;
  background-size: 100% 100% !important;
  flex-shrink: 0;
}
.btn-order::after {
  display: none !important;
}
.divider {
  height: 1rpx;
  background: #ececec;
  margin: 24rpx 32rpx;
}
.service-filters {
  width: 95vw;
  max-width: 700rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  margin: 30rpx auto 0 auto;
  z-index: 10;
}
.box_9 {
  background-color: rgba(226,224,244,1.000000);
  border-radius: 25rpx;
  position: relative;
  width: 169rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
}
.text-wrapper_1 {
  width: 115rpx;
  height: 51rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 21rpx 0 0 20rpx;
}
.text_9 {
  width: 54rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(30,30,30,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}
.text_10 {
  width: 114rpx;
  height: 15rpx;
  overflow-wrap: break-word;
  color: rgba(161,170,190,1);
  font-size: 18rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 9rpx 0 0 1rpx;
}
.image_12 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 169rpx;
  height: 90rpx;
}
.service-image {
  width: 169rpx;
  height: 90rpx;
}
.image-wrapper_4 {
  height: 90rpx;
  margin-left: 9rpx;
  display: flex;
  flex-direction: column;
  width: 169rpx;
}
