<view wx:if="{{show}}" class="popup-mask" catchtap="onMaskTap"></view>
<view class="popup-container {{popupClass}}">
  <view class="page">
    <view class="block_1">
      <view class="box_1">
        <text lines="1" class="text_1">预约到店时间</text>
        <image src="https://oss.csdu.net/ztl/images/close.png" class="label_1" bindtap="onMaskTap"></image>
      </view>
      <text lines="1" class="text_2">预约服务日期</text>
      <view class="box_2">
        <view class="text-wrapper_1" data-date="今日" bindtap="onDateTap">
          <text lines="1" class="text_3">今日</text>
          <text lines="1" class="text_4">7月12日</text>
        </view>
        <view class="text-wrapper_2" data-date="星期日" bindtap="onDateTap">
          <text lines="1" class="text_5">星期日</text>
          <text lines="1" class="text_6">7月13日</text>
        </view>
        <view class="text-wrapper_3" data-date="星期一" bindtap="onDateTap">
          <text lines="1" class="text_7">星期一</text>
          <text lines="1" class="text_8">7月14日</text>
        </view>
        <view class="text-wrapper_4" data-date="星期二" bindtap="onDateTap">
          <text lines="1" class="text_9">星期二</text>
          <text lines="1" class="text_10">7月15日</text>
        </view>
        <view class="text-wrapper_5" data-date="星期三" bindtap="onDateTap">
          <text lines="1" class="text_11">星期三</text>
          <text lines="1" class="text_12">7月16日</text>
        </view>
      </view>
      <text lines="1" class="text_13">到店时间段</text>
      <text lines="1" class="text_14">请在预约时间范围内到店，预约后可修改</text>
      <view class="box_3">
        <view class="text-wrapper_6" data-time="09:00-10:00" bindtap="onTimeTap">
          <text lines="1" class="text_15">09:00-10:00</text>
        </view>
        <view class="text-wrapper_7" data-time="10:00-11:00" bindtap="onTimeTap">
          <text lines="1" class="text_16">10:00-11:00</text>
        </view>
      </view>
      <view class="box_4">
        <view class="text-wrapper_8" data-time="11:00-12:00" bindtap="onTimeTap">
          <text lines="1" class="text_17">11:00-12:00</text>
        </view>
        <view class="text-wrapper_9" data-time="12:00-13:00" bindtap="onTimeTap">
          <text lines="1" class="text_18">12:00-13:00</text>
        </view>
      </view>
      <view class="box_5">
        <view class="text-wrapper_10" data-time="13:00-14:00" bindtap="onTimeTap">
          <text lines="1" class="text_19">13:00-14:00</text>
        </view>
        <view class="text-wrapper_11" data-time="14:00-15:00" bindtap="onTimeTap">
          <text lines="1" class="text_20">14:00-15:00</text>
        </view>
      </view>
      <view class="box_6">
        <view class="text-wrapper_12" data-time="15:00-16:00" bindtap="onTimeTap">
          <text lines="1" class="text_21">15:00-16:00</text>
        </view>
        <view class="text-wrapper_13" data-time="16:00-17:00" bindtap="onTimeTap">
          <text lines="1" class="text_22">16:00-17:00</text>
        </view>
      </view>
      <view class="box_7">
        <view class="text-wrapper_14" data-time="17:00-18:00" bindtap="onTimeTap">
          <text lines="1" class="text_23">17:00-18:00</text>
        </view>
        <view class="text-wrapper_15" data-time="18:00-19:00" bindtap="onTimeTap">
          <text lines="1" class="text_24">18:00-19:00</text>
        </view>
      </view>
      <view class="text-wrapper_16" bindtap="onConfirm">
        <text lines="1" class="text_25">确认</text>
      </view>
    </view>
  </view>
</view> 