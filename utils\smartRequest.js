import { getApiType } from '../config/apiConfig.js';
import request from './request.js';
import auth from './auth.js';
import tokenRefreshManager from './tokenRefreshManager.js';

// 智能请求函数
const smartRequest = async (options, fallbackData = null) => {
  const apiType = getApiType(options.url);
  
  switch (apiType) {
    case 'required':
      return await requiredAuthRequest(options);
    case 'optional':
      return await optionalAuthRequest(options, fallbackData);
    case 'public':
      return await publicRequest(options);
    default:
      return await optionalAuthRequest(options, fallbackData);
  }
};

// 必需登录的请求
const requiredAuthRequest = async (options) => {
  // 检查登录状态
  if (!auth.isLoggedIn()) {
    auth.redirectToLoginPage();
    throw new Error('需要登录');
  }
  
  // 尝试刷新token
  try {
    if (tokenRefreshManager.shouldRefresh()) {
      await tokenRefreshManager.refreshIfNeeded();
    }
  } catch (error) {
    // 刷新失败，跳转登录页
    auth.redirectToLoginPage();
    throw error;
  }
  
  // 发送请求
  return await request(options);
};

// 可选登录的请求
const optionalAuthRequest = async (options, fallbackData = null) => {
  try {
    // 静默检查token
    if (tokenRefreshManager.shouldRefresh()) {
      await tokenRefreshManager.refreshIfNeeded();
    }
    
    // 尝试发送请求
    const result = await request(options);
    return result;
  } catch (error) {
    // 请求失败，返回默认数据
    console.log('可选登录请求失败，使用默认数据:', error);
    return {
      code: 200,
      data: fallbackData || {}
    };
  }
};

// 公开请求
const publicRequest = async (options) => {
  return await request(options);
};

export default smartRequest; 