import { createPage } from '../../utils/pageBase.js';
import request from '../../utils/request.js';

Page(createPage({
  data: {
    userInfo: null,
    loading: true
  },

  onLoad(options) {
    // 基类已经检查了登录状态，确保用户已登录
    this.loadUserData();
  },

  async loadUserData() {
    try {
      // 使用普通request，因为已经确保登录了
      const result = await request({
        url: '/user/profile',
        method: 'GET'
      });
      
      if (result.code === 200) {
        this.setData({
          userInfo: result.data,
          loading: false
        });
      } else {
        wx.showToast({
          title: result.message || '获取用户信息失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      wx.showToast({
        title: '获取用户信息失败',
        icon: 'none'
      });
    }
  },

  // 退出登录
  handleLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const auth = require('../../utils/auth.js');
          auth.clearLoginState();
          
          wx.reLaunch({
            url: '/pages/home/<USER>'
          });
        }
      }
    });
  },

  // 跳转到我的订单
  handleMyOrders() {
    wx.navigateTo({
      url: '/pages/my-order/my-order'
    });
  },

  // 跳转到绑定车辆
  handleBindCar() {
    wx.navigateTo({
      url: '/pages/bind-car/bind-car'
    });
  }
})); 