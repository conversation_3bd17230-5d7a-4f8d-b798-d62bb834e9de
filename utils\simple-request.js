import config from './config'

/**
 * 简化版网络请求工具
 * @param {Object} options 请求参数
 * @param {String} options.url 请求地址
 * @param {String} options.method 请求方法 GET/POST
 * @param {Object} options.data 请求数据
 * @param {Object} options.header 请求头
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    // 获取token
    const token = wx.getStorageSync('token') || '';
    
    // 构建请求头
    const header = {
      'Content-Type': 'application/json',
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      header['Authorization'] = `Bearer ${token}`;
    }
    
    // 构建完整URL
    const url = options.url.startsWith('http') ? options.url : config.baseUrl + options.url;
    
    console.log('发起请求:', {
      url: url,
      method: options.method || 'GET',
      data: options.data,
      header: header
    });
    
    wx.request({
      url: url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      success: (res) => {
        console.log('请求成功:', res);
        
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        reject(new Error('网络请求失败'));
      }
    });
  });
};

export default request;