.popup-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 999;
}
.popup-container {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: transparent;
  z-index: 1000;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: flex-end;
  transition: transform 0.3s cubic-bezier(.34,1.56,.64,1);
  transform: translateY(100%);
}
.popup-show {
  transform: translateY(0);
}
.popup-hide {
  transform: translateY(100%);
}
/* 下面是原有样式 */ 
.page {
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.block_1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 25rpx;
  width: 750rpx;
  height: 1136rpx;
  margin-top: 488rpx;
  display: flex;
  flex-direction: column;
}
.box_1 {
  width: 440rpx;
  height: 31rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 42rpx 0 0 278rpx;
}
.text_1 {
  width: 192rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
}
.label_1 {
  width: 26rpx;
  height: 26rpx;
  margin-top: 1rpx;
}
.text_2 {
  width: 165rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 52rpx 0 0 31rpx;
}
.box_2 {
  width: 685rpx;
  height: 142rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 33rpx 0 0 33rpx;
}
.text-wrapper_1 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  width: 125rpx;
  height: 142rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
}
.text_3 {
  width: 44rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(216,216,216,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 35rpx 0 0 36rpx;
}
.text_4 {
  width: 82rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(216,216,216,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 32rpx 0 30rpx 20rpx;
}
.text-wrapper_2 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  width: 125rpx;
  height: 142rpx;
  margin-left: 15rpx;
  display: flex;
  flex-direction: column;
}
.text_5 {
  width: 67rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(3,3,14,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 35rpx 0 0 28rpx;
}
.text_6 {
  width: 82rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(3,3,14,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 32rpx 0 30rpx 21rpx;
}
.text-wrapper_3 {
  background-image: linear-gradient(150deg, rgba(53,68,173,1.000000) 0, rgba(53,68,173,1.000000) 0, rgba(111,115,228,1.000000) 100.000000%, rgba(111,115,228,1.000000) 100.000000%);
  border-radius: 5rpx;
  width: 125rpx;
  height: 142rpx;
  margin-left: 15rpx;
  display: flex;
  flex-direction: column;
}
.text_7 {
  width: 70rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 35rpx 0 0 21rpx;
}
.text_8 {
  width: 82rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 32rpx 0 30rpx 21rpx;
}
.text-wrapper_4 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  width: 125rpx;
  height: 142rpx;
  margin-left: 15rpx;
  display: flex;
  flex-direction: column;
}
.text_9 {
  width: 70rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(3,3,14,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 35rpx 0 0 21rpx;
}
.text_10 {
  width: 82rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(3,3,14,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 32rpx 0 30rpx 21rpx;
}
.text-wrapper_5 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  width: 125rpx;
  height: 142rpx;
  border: 1px solid rgba(236,236,236,1);
  margin-left: 15rpx;
  display: flex;
  flex-direction: column;
}
.text_11 {
  width: 70rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(216,216,216,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 35rpx 0 0 22rpx;
}
.text_12 {
  width: 82rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(216,216,216,1);
  font-size: 24rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 32rpx 0 30rpx 22rpx;
}
.text_13 {
  width: 139rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 52rpx 0 0 31rpx;
}
.text_14 {
  width: 360rpx;
  height: 21rpx;
  overflow-wrap: break-word;
  color: rgba(104,105,125,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 24rpx 0 0 31rpx;
}
.box_3 {
  width: 687rpx;
  height: 74rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 0 31rpx;
}
.text-wrapper_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_15 {
  width: 159rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(180,182,184,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_7 {
  border-radius: 5rpx;
  height: 74rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_16 {
  width: 148rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(180,182,184,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.box_4 {
  width: 687rpx;
  height: 74rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 19rpx 0 0 31rpx;
}
.text-wrapper_8 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_17 {
  width: 148rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(3,3,14,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_9 {
  background-image: linear-gradient(90deg, rgba(53,68,173,1.000000) 0, rgba(53,68,173,1.000000) 0, rgba(111,115,228,1.000000) 100.000000%, rgba(111,115,228,1.000000) 100.000000%);
  border-radius: 5rpx;
  height: 74rpx;
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_18 {
  width: 154rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 28rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.box_5 {
  width: 687rpx;
  height: 74rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 18rpx 0 0 31rpx;
}
.text-wrapper_10 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_19 {
  width: 154rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(180,182,184,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_11 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_20 {
  width: 154rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(8,17,34,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.box_6 {
  width: 687rpx;
  height: 74rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 19rpx 0 0 31rpx;
}
.text-wrapper_12 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_21 {
  width: 154rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(180,182,184,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_13 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_22 {
  width: 152rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(8,17,34,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.box_7 {
  width: 687rpx;
  height: 74rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 18rpx 0 0 31rpx;
}
.text-wrapper_14 {
  background-color: rgba(230,237,253,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_23 {
  width: 152rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(8,17,34,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_15 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 5rpx;
  height: 74rpx;
  border: 1px solid rgba(236,236,236,1);
  display: flex;
  flex-direction: column;
  width: 336rpx;
}
.text_24 {
  width: 154rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(180,182,184,1);
  font-size: 28rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 26rpx 0 0 31rpx;
}
.text-wrapper_16 {
  height: 86rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAr4AAABWCAYAAAApD5WzAAAACXBIWXMAAAsTAAALEwEAmpwYAAAHMWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78i 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) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 702rpx;
  margin: 58rpx 0 69rpx 24rpx;
}
.text_25 {
  width: 65rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 28rpx 0 0 319rpx;
} 