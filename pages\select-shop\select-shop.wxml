<view class="page">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top:{{statusBarHeight}}px;">
    <view class="navbar-content">
      <view class="navbar-left" bindtap="onBackTap">
        <image src="https://oss.csdu.net/ztl/images/arrow_left.png" class="back-icon"></image>
      </view>
      <view class="navbar-title">门店选择</view>
      <view class="navbar-right"></view>
    </view>
  </view>

  <!-- 页面内容 -->
  <view class="page-content" style="margin-top:{{statusBarHeight + 44}}px;">
    <!-- 搜索区域 -->
    <view class="search-section">
    <view class="location-info">
      <image src="https://oss.csdu.net/ztl/images/dingwei.png" class="location-icon"></image>
      <text class="location-text">深圳市</text>
      <image src="https://oss.csdu.net/ztl/images/arrow_down.png" class="arrow-down"></image>
    </view>
    <view class="search-box" bindtap="onSearchTap">
      <image src="https://oss.csdu.net/ztl/images/search.png" class="search-icon"></image>
      <text class="search-placeholder">搜索网点名称或地址</text>
    </view>
  </view>

  <!-- 门店列表 -->
  <view class="shop-list">
    <view class="shop-item" wx:for="{{shops}}" wx:key="index" bindtap="selectShop" data-index="{{index}}">
      <!-- 选择状态指示器 -->
      <view class="selection-indicator">
        <view class="radio-button {{item.selected ? 'selected' : ''}}">
          <view class="radio-inner" wx:if="{{item.selected}}"></view>
        </view>
      </view>

      <!-- 门店图片 -->
      <view class="shop-image">
        <image src="https://oss.csdu.net/ztl/images/default_shop.png" class="shop-image-content" mode="aspectFill"></image>
      </view>

      <!-- 门店信息 -->
      <view class="shop-info">
        <view class="shop-header">
          <text class="shop-name">{{item.name}}</text>
        </view>
        
        <view class="shop-details">
          <view class="rating-section">
            <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
            <text class="rating-text">{{item.rating}}</text>
          </view>
          
          <view class="business-hours">
            <text class="hours-label">营业时间：</text>
            <text class="hours-text">{{item.businessHours}}</text>
          </view>
          
          <view class="distance-section">
            <image src="https://oss.csdu.net/ztl/images/dingwei.png" class="distance-icon"></image>
            <text class="distance-text">{{item.distance}}</text>
          </view>
        </view>
        
        <view class="shop-address">
          <text class="address-text">{{item.address}}</text>
        </view>
      </view>
    </view>
  </view>
  </view>
</view> 