import { getPageType } from '../config/pageConfig.js';
import auth from './auth.js';
import smartRequest from './smartRequest.js';

// 页面基类
export function createPage(pageConfig) {
  return {
    ...pageConfig,
    
    onLoad(options) {
      // 检查页面类型
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const pageType = getPageType(currentPage.route);
      
      if (pageType === 'required') {
        // 必需登录页面
        if (!auth.isLoggedIn()) {
          auth.redirectToLoginPage();
          return;
        }
      }
      
      // 调用原onLoad
      if (pageConfig.onLoad) {
        pageConfig.onLoad.call(this, options);
      }
    },
    
    // 智能数据加载
    async loadData(url, fallbackData = null) {
      try {
        const result = await smartRequest({
          url: url,
          method: 'GET'
        }, fallbackData);
        
        return result;
      } catch (error) {
        console.error('数据加载失败:', error);
        return { code: 200, data: fallbackData };
      }
    },

    // 智能POST请求
    async postData(url, data, fallbackData = null) {
      try {
        const result = await smartRequest({
          url: url,
          method: 'POST',
          data: data
        }, fallbackData);
        
        return result;
      } catch (error) {
        console.error('数据提交失败:', error);
        return { code: 200, data: fallbackData };
      }
    }
  };
} 