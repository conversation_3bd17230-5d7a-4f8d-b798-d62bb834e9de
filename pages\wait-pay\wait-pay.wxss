/* pages/wait-pay/wait-pay.wxss */

/* ===== 自定义导航栏 ===== */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 88rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  z-index: 1000;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.navbar-content {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 24rpx;
  flex: 1;
  position: relative;
}

.navbar-left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 25rpx;
  position: absolute;
  left: 24rpx;
  z-index: 10;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
}

.home-icon {
  width: 32rpx;
  height: 32rpx;
}

.navbar-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 31rpx;
  font-weight: 500;
  color: #252027;
  line-height: 31rpx;
  z-index: 5;
}

.navbar-right {
  position: absolute;
  right: 24rpx;
  width: 60rpx;
  z-index: 10;
}

.navbar-spacer {
  width: 750rpx;
  background: #f4f4f4;
  display: block;
  position: relative;
  left: 0;
  margin: 0;
  padding: 0;
}

/* ===== 页面主体 ===== */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  background-color: #f4f4f4;
  display: flex;
  flex-direction: column;
}

/* ===== 倒计时区域 ===== */
.countdown-section {
  background-color: #fff;
  border-radius: 15rpx;
  margin: 20rpx 22rpx;
  padding: 30rpx 0;
}

.countdown-content {
  text-align: center;
  font-size: 24rpx;
  color: #252027;
}

.countdown-text {
  color: #252027;
}

.countdown-time {
  color: #f44949;
  font-weight: 500;
}

.countdown-unit {
  color: #252027;
}

/* ===== 订单信息区域 ===== */
.order-section {
  margin: 0 22rpx;
}

/* 门店信息卡片 */
.shop-card {
  background-color: #fff;
  border-radius: 25rpx;
  padding: 37rpx 22rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shop-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
}

.shop-icon {
  width: 30rpx;
  height: 28rpx;
  margin-right: 22rpx;
  margin-top: 2rpx;
}

.shop-details {
  flex: 1;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.2;
  margin-bottom: 17rpx;
  display: block;
}

.shop-address {
  display: flex;
  flex-direction: column;
}

.address-text,
.address-detail {
  font-size: 24rpx;
  color: #838383;
  line-height: 1.5;
}

.shop-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-left: 34rpx;
}

.action-icon {
  width: 46rpx;
  height: 46rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #343434;
  font-weight: 500;
}

/* 服务信息卡片 */
.service-card {
  background-color: #fff;
  border-radius: 25rpx;
  padding: 29rpx 25rpx;
  margin-bottom: 16rpx;
}

.service-info {
  display: flex;
  align-items: center;
}

.service-icon {
  width: 140rpx;
  height: 140rpx;
  background-color: #6677b3;
  border-radius: 25rpx;
  margin-right: 27rpx;
}

.service-details {
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #121f2b;
  line-height: 1.2;
  margin-bottom: 16rpx;
  display: block;
}

.service-price {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 24rpx;
  color: #e23d5d;
  font-weight: 600;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 36rpx;
  color: #e23d5d;
  font-weight: 700;
}

/* 价格明细卡片 */
.price-card {
  background-color: #fff;
  border-radius: 25rpx;
  padding: 29rpx 30rpx;
  margin-bottom: 16rpx;
}

.price-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #121f2b;
  margin-bottom: 30rpx;
  display: block;
}

.price-details {
  margin-bottom: 25rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-label {
  font-size: 24rpx;
  color: #202020;
}

.price-value {
  font-size: 24rpx;
  color: #202020;
}

.price-discount {
  font-size: 24rpx;
  color: #e23d5d;
}

.price-divider {
  height: 1rpx;
  background-color: #ececec;
  margin: 25rpx 0;
}

.price-summary {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 38rpx;
}

.summary-row {
  display: flex;
  align-items: center;
}

.summary-label {
  font-size: 24rpx;
  color: #838383;
  margin-right: 7rpx;
}

.summary-value {
  font-size: 28rpx;
  color: #e23d5d;
  font-weight: 500;
}

.summary-total {
  font-size: 28rpx;
  color: #e23d5d;
  font-weight: 500;
}

/* 订单编号卡片 */
.order-number-card {
  background-color: #fff;
  border-radius: 25rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
}

.order-number {
  font-size: 24rpx;
  color: #202020;
  margin-right: 8rpx;
}

.copy-icon {
  width: 20rpx;
  height: 11rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  background-color: #ecebf7;
  border: 1rpx solid #7e7bb0;
  border-radius: 5rpx;
  padding: 8rpx 23rpx;
}

.copy-text {
  font-size: 20rpx;
  color: #1e1e1e;
  font-weight: 500;
}

/* ===== 底部操作按钮 ===== */
.action-section {
  background-color: #fff;
  padding: 34rpx 22rpx;
  display: flex;
  justify-content: flex-end;
  gap: 18rpx;
  margin-top: 20rpx;
}

.cancel-btn {
  width: 174rpx;
  height: 70rpx;
  border: 1rpx solid #dad9ec;
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pay-btn {
  width: 174rpx;
  height: 70rpx;
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn .btn-text {
  color: #838383;
}

.pay-btn .btn-text {
  color: #fff;
}