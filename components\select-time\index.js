Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
      observer(newVal) {
        if (newVal) {
          this.setData({ popupClass: 'popup-show' });
        } else {
          this.setData({ popupClass: 'popup-hide' });
        }
      }
    }
  },
  data: {
    popupClass: '',
    selectedDate: null,
    selectedTime: null
  },
  lifetimes: {
    attached: function () {
      // 页面加载
    },
    detached: function () {
      // 页面卸载
    },
  },
  methods: {
    onMaskTap() {
      this.triggerEvent('close');
    },
    onDateTap(e) {
      this.setData({ selectedDate: e.currentTarget.dataset.date });
    },
    onTimeTap(e) {
      this.setData({ selectedTime: e.currentTarget.dataset.time });
    },
    onConfirm() {
      if (this.data.selectedDate && this.data.selectedTime) {
        this.triggerEvent('select', {
          date: this.data.selectedDate,
          time: this.data.selectedTime
        });
        this.setData({ selectedDate: null, selectedTime: null });
      }
    }
  }
}); 