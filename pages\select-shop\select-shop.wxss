/* 页面基础样式 */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25rpx;
}

.navbar-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
  margin-left: 25rpx;
}

.navbar-title {
  font-size: 32rpx;
  font-weight: 500;
  line-height: 16rpx;
  color: #333333;
  flex: 1;
  text-align: center;
}

.navbar-right {
  width: 60rpx;
}

/* 页面内容 */
.page-content {
  padding: 0 0rpx 20rpx 0rpx;
  flex: 1;
}

/* 搜索区域 */
.search-section {
  padding: 25rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 120rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 100;
}

.location-info {
  display: flex;
  align-items: center;
  min-height: 48rpx;
  padding: 0rpx 0rpx;
  border-radius: 20rpx;
  width: fit-content;
  max-width: 200rpx;
  margin-right: 16rpx;
}

.location-icon {
  width: 20rpx;
  height: 24rpx;
  margin-right: 8rpx;
  margin-left: 25rpx;
}

.location-text {
  font-size: 26rpx;
  color: #252027;
  font-weight: 500;
  line-height: 11rpx;
  margin-right: 6rpx;
}

.arrow-down {
  width: 13rpx;
  height: 8rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 24rpx;
  padding: 20rpx 24rpx;
  transition: all 0.3s ease;
  flex: 1;
}

.search-box:active {
  background-color: #e8e8e8;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  font-size: 28rpx;
  color: #999999;
  flex: 1;
}

/* 门店列表 */
.shop-list {
  flex: 1;
  padding: 0 0 32rpx 0;
  margin-top: 19rpx;
}

.shop-item {
  background-color: #ffffff;
  margin-bottom: 18rpx;
  padding: 17rpx 25rpx;
  height: 192rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.shop-item:active {
  transform: scale(0.98);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 选择状态指示器 */
.selection-indicator {
  margin-right: 22rpx;
  margin-top: 17rpx;
}

.radio-button {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.radio-button.selected {
  border-color: #6677b3;
  background-color: #6677b3;
}

.radio-inner {
  width: 16rpx;
  height: 16rpx;
  background-color: #ffffff;
  border-radius: 50%;
}

/* 门店图片 */
.shop-image {
  margin-right: 17rpx;
}

.shop-image-content {
  width: 156rpx;
  height: 156rpx;
  border-radius: 25rpx;
  background-color: #6677b3;
}

/* 门店信息 */
.shop-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 17rpx;
  margin-right: 17rpx;
}

.shop-header {
  margin-bottom: 12rpx;
}

.shop-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e1e1e;
  line-height: 1.4;
}

.shop-details {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.rating-section {
  display: flex;
  align-items: center;
  margin-right: 17rpx;
}

.star-icon {
  width: 21rpx;
  height: 20rpx;
  margin-right: 8rpx;
}

.rating-text {
  font-size: 22rpx;
  color: #e23d5d;
  font-weight: 600;
}

.business-hours {
  display: flex;
  align-items: center;
  margin-right: 63rpx;
}

.hours-label {
  font-size: 20rpx;
  color: #343434;
  margin-right: 4rpx;
}

.hours-text {
  font-size: 22rpx;
  color: #343434;
}

.distance-section {
  display: flex;
  align-items: center;
}

.distance-icon {
  width: 19rpx;
  height: 22rpx;
  margin-right: 8rpx;
}

.distance-text {
  font-size: 24rpx;
  color: #343434;
}

.shop-address {
  margin-top: -5rpx;
}

.address-text {
  font-size: 24rpx;
  color: #898989;
  line-height: 1.4;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .shop-details {
    gap: 16rpx;
  }
  
  .shop-item {
    padding: 24rpx;
  }
  
  .shop-image-content {
    width: 120rpx;
    height: 120rpx;
  }
} 