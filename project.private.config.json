{"condition": {"miniprogram": {"list": [{"name": "登录页面", "pathName": "pages/login/login", "query": "", "scene": null, "launchMode": "default"}, {"name": "订单列表", "pathName": "pages/my-order/my-order", "query": "", "launchMode": "default", "scene": null}, {"name": "绑定车辆", "pathName": "pages/bind-car/bind-car", "query": "", "launchMode": "default", "scene": null}, {"name": "门店详情", "pathName": "pages/shop-detail/shop-detail", "query": "shopId=1", "launchMode": "default", "scene": null}, {"name": "待支付", "pathName": "pages/wait-pay/wait-pay", "query": "", "launchMode": "default", "scene": null}, {"name": "选择商品", "pathName": "pages/select-spu/select-spu", "query": "", "launchMode": "default", "scene": null}, {"name": "选择门店", "pathName": "pages/select-shop/select-shop", "query": "", "launchMode": "default", "scene": null}, {"name": "服务进度", "pathName": "pages/service-progress/service-progress", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/home/<USER>", "pathName": "pages/home/<USER>", "query": "", "launchMode": "default", "scene": null}, {"name": "登录页", "pathName": "pages/login/login", "query": "", "launchMode": "default", "scene": null}]}}, "setting": {"bigPackageSizeSupport": true, "urlCheck": false, "coverView": true, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "useIsolateContext": true}, "libVersion": "3.0.0", "projectname": "ztlskin"}