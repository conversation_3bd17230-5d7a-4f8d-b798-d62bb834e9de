<view class="page" style="padding-top:{{statusBarHeight}}px;">
  <image class="page-bg" src="https://oss.csdu.net/ztl/images/yuyue_bg.png" mode="scaleToFill"></image>

  <!-- 页面标题 -->
  <view class="page-header">
    <text lines="1" class="page-title">我要预约</text>
  </view>

  <!-- 新增图片 -->
  <image class="top-image" src="https://oss.csdu.net/ztl/images/yuyuefuwu.png" mode="aspectFit"></image>

  <!-- 服务特色说明 -->
  <view class="service-features">
    <view class="feature-item feature-item-1">
      <image class="feature-icon" src="https://oss.csdu.net/ztl/images/duigou.png" mode="aspectFit"></image>
      <text class="feature-text">到店优先排队</text>
    </view>
    <view class="feature-item feature-item-2">
      <image class="feature-icon" src="https://oss.csdu.net/ztl/images/duigou.png" mode="aspectFit"></image>
      <text class="feature-text">节省等待时间</text>
    </view>
    <view class="feature-item feature-item-3">
      <image class="feature-icon" src="https://oss.csdu.net/ztl/images/duigou.png" mode="aspectFit"></image>
      <text class="feature-text">预约后可修改</text>
    </view>
  </view>

  <!-- 车辆信息卡片 -->
  <view class="vehicle-card">
    <!-- 当前车辆标签 -->
    <view class="vehicle-header">
      <image src="https://oss.csdu.net/ztl/images/che_tag_bg.png" class="vehicle-label-bg"></image>
      <text lines="1" class="vehicle-label">当前车辆</text>
    </view>
    
    <!-- 车辆主要信息 -->
    <view class="vehicle-info">
      <image src="https://oss.csdu.net/ztl/images/vehicle_image.png" class="vehicle-image"></image>
      <view class="vehicle-details">
        <view class="vehicle-name">
          <text lines="1" decode="true" class="car-brand">福特&nbsp;蒙迪欧</text>
          <image src="https://oss.csdu.net/ztl/images/edit_tag.png" class="edit-icon"></image>
        </view>
        <text lines="1" decode="true" class="car-model">2025款&nbsp;1.5T&nbsp;Z自动EcoBoost&nbsp;时尚型</text>
      </view>
    </view>
    
    <!-- 车牌信息区域 -->
    <view class="license-plate">
      <view class="plate-number">
        <text lines="1" class="plate-prefix">湘D</text>
        <text lines="1" class="plate-suffix">3654P1</text>
      </view>
      <view class="auth-status">
        <text lines="1" class="auth-text">未认证</text>
      </view>
    </view>
  </view>

  <!-- 预约信息表单 -->
  <view class="appointment-form">
    <view class="form-header">
      <image src="https://oss.csdu.net/ztl/images/record.png" class="form-icon"></image>
      <text class="form-title">预约信息</text>
    </view>
    
    <view class="form-item" bindtap="selectStore">
      <text class="form-label">选择门店</text>
      <view class="form-value">
        <text class="form-placeholder" wx:if="{{!selectedShop}}">请选择</text>
        <text class="form-selected" wx:if="{{selectedShop}}">{{selectedShop.name}}</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="arrow-icon"></image>
      </view>
    </view>
    
    <view class="form-item" bindtap="selectService">
      <text class="form-label">选择服务</text>
      <view class="form-value">
        <text class="form-placeholder">请选择</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="arrow-icon"></image>
      </view>
    </view>
    
    <view class="form-item" bindtap="selectTime">
      <text class="form-label">期望时间</text>
      <view class="form-value">
        <text class="form-selected">2025-07-23 16:00-17:00</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_right.png" class="arrow-icon"></image>
      </view>
    </view>
    <!-- 选择时间弹窗组件 -->
    <select-time show="{{showSelectTime}}" bind:close="onSelectTimeClose" bind:select="onTimeSelected" />
  </view>

  <!-- 底部支付按钮 -->
  <view class="payment-section">
    <view class="payment-button" bindtap="payNow">
      <text class="payment-text">立即支付 ￥40</text>
    </view>
  </view>


</view>