Page({
  data: {
    statusBarHeight: 44,
    statusBarHeightRpx: 88,
    // 车牌号输入
    plateNumber: {
      province: '',
      letter: '',
      letter2: '',
      letter3: '',
      letter4: '',
      letter5: '',
      letter6: ''
    },
    // 用户协议
    agreed: false
  },

  onLoad: function (options) {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight; // px
    const screenWidth = systemInfo.screenWidth; // px
    // 1px = 750 / screenWidth rpx
    const px2rpx = 750 / screenWidth;
    const statusBarHeightRpx = Math.round(statusBarHeight * px2rpx);
    this.setData({ statusBarHeight, statusBarHeightRpx });
    
    console.info("绑定车辆页面加载");
  },

  onShow: function () {
    // 页面显示时的逻辑
  },

  onUnload: function () {
    console.info("绑定车辆页面卸载");
  },

  // 返回上一页
  onBackTap: function() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/home/<USER>'
      });
    }
  },

  // 首页按钮点击事件
  onHomeTap: function() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    });
  },

  // 车牌号变化
  onPlateChange: function(e) {
    const { plateNumber } = e.detail;
    this.setData({ plateNumber });
  },

  // 省份变化
  onProvinceChange: function(e) {
    const { province } = e.detail;
  },



  // 切换用户协议
  onAgreementToggle: function() {
    this.setData({
      agreed: !this.data.agreed
    });
  },

  // 用户协议链接
  onAgreementLink: function() {
    wx.showModal({
      title: '用户信息授权说明',
      content: '紫太狼养车将严格按照相关法律法规保护您的个人信息，车牌信息仅用于车型识别和适配服务，不会用于其他用途。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 提交绑定
  onSubmit: function(e) {
    const { plateNumber, plateType } = e.detail;
    const { agreed } = this.data;
    
    if (!agreed) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      });
      return;
    }

    // 验证车牌号
    const fullPlate = plateNumber.province + plateNumber.letter + plateNumber.letter2 + 
                     plateNumber.letter3 + plateNumber.letter4 + plateNumber.letter5 + plateNumber.letter6;
    
    if (fullPlate.length < 7) {
      wx.showToast({
        title: '请输入完整的车牌号',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '识别中...'
    });

    // 模拟API调用
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '识别成功',
        icon: 'success'
      });
      
      // 跳转到车型确认页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/car-confirm/car-confirm?plateNumber=' + fullPlate + '&plateType=' + plateType
        });
      }, 1500);
    }, 2000);
  },

  // 手动选择车型
  onManualSelect: function() {
    wx.navigateTo({
      url: '/pages/manual-select/manual-select'
    });
  },

  // 扫描证件
  onScanDocument: function() {
    wx.scanCode({
      success: (res) => {
        wx.showToast({
          title: '扫描成功',
          icon: 'success'
        });
        // 处理扫描结果
      },
      fail: (err) => {
        wx.showToast({
          title: '扫描失败',
          icon: 'none'
        });
      }
    });
  }
}); 