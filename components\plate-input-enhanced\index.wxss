/* components/plate-input-enhanced/index.wxss */

/* 车牌输入区域 */
.plate-form-section {
  margin: 300rpx 24rpx 32rpx;
  position: relative;
  z-index: 3;
}

.form-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  text-align: left;
}

.form-hint {
  font-size: 26rpx;
  color: #999;
  text-align: left;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 车牌输入框 */
.plate-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.plate-input-group {
  display: flex;
  align-items: center;
}



.plate-input.active {
  border-color: #667eea;
  background: #f0f4ff;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.2);
}

.plate-input.filled {
  border-color: #52c41a;
  background: #f6ffed;
}

.plate-input .placeholder {
  color: #ccc;
  font-size: 24rpx;
}

/* 输入框样式 */
.plate-input {
  width: 69rpx;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background: #fff;
  margin-right: 7rpx;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.plate-input .placeholder {
  color: #ccc;
  font-size: 24rpx;
}

.plate-separator {
  width: 8rpx;
  height: 8rpx;
  background-color: #333;
  border-radius: 50%;
  margin: 0 7rpx;
  display: inline-block;
}

.new-energy-box {
  width: 69rpx;
  height: 80rpx;
  border: 2rpx dashed #52c41a;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 0;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.new-energy-text {
  font-size: 18rpx;
  color: #52c41a;
  text-align: center;
}

.new-energy-input {
  font-size: 32rpx;
  color: #52c41a;
  text-align: center;
  font-weight: bold;
}

/* 车牌验证状态 */
.plate-validation {
  margin-bottom: 24rpx;
}

.validation-status {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.validation-status.valid {
  background: #f6ffed;
  color: #52c41a;
}

.validation-status.invalid {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 用户协议 */
.agreement-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 0;
}

.agreement-checkbox {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
}

.checkbox-empty {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
}

.agreement-text {
  font-size: 28rpx;
  color: #666;
  text-align: left;
}

.agreement-link {
  font-size: 28rpx;
  color: #ff4757;
  text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.submit-btn.disabled {
  background: #f0f0f0;
  color: #999;
}

/* 其他添加方式 */
.alternative-section {
  padding: 0 24rpx 120rpx;
}

.alternative-title {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.alternative-btn {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.alternative-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.btn-arrow {
  width: 16rpx;
  height: 30rpx;
  color: #130F35;
}

.btn-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.accuracy-tag {
  width: 194rpx;
  height: 37rpx;
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 19rpx 19rpx 19rpx 0rpx;
  padding: 4rpx 12rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-text {
  font-size: 22rpx;
  color: #ffffff;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 弹窗样式 */
.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.picker-content {
  background: #fff;
  border-radius: 20rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { 
    transform: translateY(100rpx);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
  border-radius: 50%;
  background: #f5f5f5;
}

/* 省份选择器 */
.province-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 16rpx;
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.province-item {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.province-item:active {
  background: #667eea;
  color: #fff;
  transform: scale(0.95);
}

.province-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 字母数字选择器 */
.letter-section, .number-section {
  padding: 24rpx 32rpx;
}

.section-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.letter-grid, .number-grid {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
  gap: 12rpx;
}

.letter-item, .number-item {
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.letter-item:active, .number-item:active {
  background: #667eea;
  color: #fff;
  transform: scale(0.95);
}

.letter-text, .number-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 历史记录 */
.history-list {
  padding: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.history-item {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:active {
  background: #f5f5f5;
}

.history-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.history-empty {
  padding: 64rpx 32rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 自定义键盘弹窗 */
.keyboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.keyboard-overlay.show {
  opacity: 1;
  visibility: visible;
}

.keyboard-container {
  width: 100%;
  background-color: #fff;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.keyboard-overlay.show .keyboard-container {
  transform: translateY(0);
}

/* 键盘头部 */
.keyboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #eee;
}

.keyboard-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.keyboard-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f5f5f5;
  font-size: 36rpx;
  color: #666;
  transition: background-color 0.3s ease;
}

.keyboard-close:active {
  background-color: #e0e0e0;
}

/* 当前车牌显示 */
.current-plate {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 40rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.current-item {
  width: 60rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  transition: all 0.3s ease;
}

.current-item.current-active {
  border-color: #330aec;
  background-color: #f0f8ff;
  box-shadow: 0 0 8rpx rgba(51, 10, 236, 0.3);
}

.current-placeholder {
  color: #bbb;
  font-size: 20rpx;
  font-weight: normal;
}

/* 键盘主体 */
.keyboard-body {
  padding: 30rpx 40rpx 60rpx;
}

.key-container {
  margin-bottom: 30rpx;
}

.key-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.key-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 6rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  transition: all 0.3s ease;
}

.key-item:active {
  background-color: #330aec;
  border-color: #330aec;
  color: #fff;
  transform: scale(0.95);
}

.key-item.disabled {
  background-color: transparent;
  border-color: transparent;
  color: transparent;
  pointer-events: none;
}

/* 操作按钮 */
.action-row {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.delete-btn {
  background-color: #f8f9fa;
  border: 1rpx solid #e8e8e8;
  color: #666;
}

.delete-btn:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.confirm-btn {
  background: linear-gradient(135deg, #330aec 0%, #6c5ce7 100%);
  color: #fff;
}

.confirm-btn:active {
  background: linear-gradient(135deg, #2d09cc 0%, #5a4fcf 100%);
  transform: scale(0.95);
} 