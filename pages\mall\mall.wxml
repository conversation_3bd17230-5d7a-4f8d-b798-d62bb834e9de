<view class="page {{pageScrollDisabled ? 'page-scroll-disabled' : ''}}" style="padding-top:{{statusBarHeight}}px;">
  <!-- 固定头部区域 -->
  <view class="fixed-header">
    <!-- 页面标题 -->
    <view class="page-header">
      <text lines="1" class="page-title">门店列表</text>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section">
      <view class="search-bar">
        <image src="https://oss.csdu.net/ztl/images/search.png" class="search-icon"></image>
        <text class="search-placeholder">搜索网点名称或地址</text>
      </view>
      <view class="map-btn">
        <text class="map-text">地图</text>
        <view class="map-dots">
          <image src="https://oss.csdu.net/ztl/images/dtxd.png" class="map-dots-img"></image>
        </view>
      </view>
    </view>

    <!-- 服务类型筛选 -->
    <view class="service-filters">
      <view class="service-item" bindtap="onServiceTap" data-id="baoyang">
        <image wx:if="{{selectedService === 'baoyang'}}" src="https://oss.csdu.net/ztl/images/baoyang_select.png" class="service-image"></image>
        <image wx:else src="https://oss.csdu.net/ztl/images/baoyang_bg.png" class="service-image"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-id="xiche">
        <image wx:if="{{selectedService === 'xiche'}}" src="https://oss.csdu.net/ztl/images/xiche_select.png" class="service-image"></image>
        <image wx:else src="https://oss.csdu.net/ztl/images/xiche_bg.png" class="service-image"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-id="meirong">
        <image wx:if="{{selectedService === 'meirong'}}" src="https://oss.csdu.net/ztl/images/meiron_select.png" class="service-image"></image>
        <image wx:else src="https://oss.csdu.net/ztl/images/meirong_bg.png" class="service-image"></image>
      </view>
      <view class="service-item" bindtap="onServiceTap" data-id="weixiu">
        <image wx:if="{{selectedService === 'weixiu'}}" src="https://oss.csdu.net/ztl/images/weixiu_select.png" class="service-image"></image>
        <image wx:else src="https://oss.csdu.net/ztl/images/weixiu_bg.png" class="service-image"></image>
      </view>
    </view>

    <!-- 筛选选项 -->
    <view class="filter-options">
      <view class="filter-option">
        <text class="option-text">深圳市龙...</text>
        <image src="https://oss.csdu.net/ztl/images/sanjiao.png" class="option-arrow"></image>
      </view>
      <view class="filter-option">
        <text class="option-text">智能排序</text>
        <image src="https://oss.csdu.net/ztl/images/sanjiao.png" class="option-arrow"></image>
      </view>
      <view class="filter-option">
        <text class="option-text">服务项目</text>
        <image src="https://oss.csdu.net/ztl/images/sanjiao.png" class="option-arrow"></image>
      </view>
      <view class="filter-option">
        <text class="option-text">门店筛选</text>
        <image src="https://oss.csdu.net/ztl/images/sanjiao.png" class="option-arrow"></image>
      </view>
    </view>
  </view>

  <!-- 滚动内容区域 -->
  <scroll-view class="scroll-content" scroll-y="{{!pageScrollDisabled}}">
    <!-- 门店列表 -->
    <view class="store-list">
    <!-- 门店卡片1 -->
    <view class="store-card">
      <view class="store-card-content">
        <view class="store-image"></view>
        <view class="store-info">
          <text class="store-name">美车堂COXOPARK旗舰店</text>
          <view class="store-details">
            <view class="rating">
              <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
              <text class="rating-text">4.99</text>
            </view>
            <view class="status-open">
              <text class="status-text">营业中</text>
              <view class="time-badge">
                <text class="time-text">10:00-21:00</text>
              </view>
            </view>
            <view class="distance">
              <image src="https://oss.csdu.net/ztl/images/dingweibai.png" class="location-icon"></image>
              <text class="distance-text">10.5km</text>
            </view>
          </view>
          <text class="store-address">福田区福华三路269号福田星河COCO Park...</text>
        </view>
      </view>
      
      <!-- 服务项目信息 - 只有在服务类型被选中时显示 -->
      <view class="section_2 {{selectedService ? '' : 'hidden'}}">
        <view class="block_2">
          <text lines="1" class="text_6">标准洗车-五座轿车</text>
          <view class="text-wrapper_3">
            <text lines="1" class="text_7">3小时内可服务</text>
          </view>
        </view>
        <view class="price-container" bindtap="onGrabTap" data-store="美车堂COXOPARK旗舰店" data-price="68" data-original="128">
          <view class="price-bg">
            <view class="price-gradient"></view>
            <image src=".https://oss.csdu.net/ztl/images/shandian.png" class="lightning-icon"></image>
          </view>
          <view class="price-content">
            <view class="original-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">128</view>
              </view>
              <view class="price-label">原价</view>
            </view>
            <view class="member-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">68</view>
              </view>
              <view class="price-label">会员价</view>
            </view>
            <view class="grab-text">抢</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 门店卡片2 -->
    <view class="store-card">
      <view class="store-card-content">
        <view class="store-image"></view>
        <view class="store-info">
          <text class="store-name">车享家深圳南海大道店</text>
          <view class="store-details">
            <view class="rating">
              <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
              <text class="rating-text">4.99</text>
            </view>
            <view class="status-closed">
              <text class="status-text">休息中</text>
              <view class="time-badge">
                <text class="time-text">明天09:00营业</text>
              </view>
            </view>
            <view class="distance">
              <image src="https://oss.csdu.net/ztl/images/dingweibai.png" class="location-icon"></image>
              <text class="distance-text">2.5km</text>
            </view>
          </view>
          <text class="store-address">南山区南海大道4050号上汽大厦副栋1层</text>
        </view>
      </view>
      
      <!-- 服务项目信息 - 只有在服务类型被选中时显示 -->
      <view class="section_2 {{selectedService ? '' : 'hidden'}}">
        <view class="block_2">
          <text lines="1" class="text_6">精致洗车-七座轿车</text>
          <view class="text-wrapper_3">
            <text lines="1" class="text_7">暂停服务</text>
          </view>
        </view>
        <view class="price-container" bindtap="onGrabTap" data-store="车享家深圳南海大道店" data-price="168" data-original="360">
          <view class="price-bg">
            <view class="price-gradient"></view>
            <image src="https://oss.csdu.net/ztl/images/shandian.png" class="lightning-icon"></image>
          </view>
          <view class="price-content">
            <view class="original-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">360</view>
              </view>
              <view class="price-label">原价</view>
            </view>
            <view class="member-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">168</view>
              </view>
              <view class="price-label">会员价</view>
            </view>
            <view class="grab-text">抢</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 门店卡片3 -->
    <view class="store-card">
      <view class="store-card-content">
        <view class="store-image"></view>
        <view class="store-info">
          <text class="store-name">美车堂龙华大浪店</text>
          <view class="store-details">
            <view class="rating">
              <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
              <text class="rating-text">4.99</text>
            </view>
            <view class="status-open">
              <text class="status-text">营业中</text>
              <view class="time-badge">
                <text class="time-text">10:00-21:00</text>
              </view>
            </view>
            <view class="distance">
              <image src="https://oss.csdu.net/ztl/images/dingweibai.png" class="location-icon"></image>
              <text class="distance-text">24.9km</text>
            </view>
          </view>
          <text class="store-address">龙华区同胜社区华繁路1-10号</text>
        </view>
      </view>
      
      <!-- 服务项目信息 - 只有在服务类型被选中时显示 -->
      <view class="section_2 {{selectedService ? '' : 'hidden'}}">
        <view class="block_2">
          <text lines="1" class="text_6">标准洗车-五座轿车</text>
          <view class="text-wrapper_3">
            <text lines="1" class="text_7">3小时内可服务</text>
          </view>
        </view>
        <view class="price-container" bindtap="onGrabTap" data-store="美车堂龙华大浪店" data-price="88" data-original="158">
          <view class="price-bg">
            <view class="price-gradient"></view>
            <image src="https://oss.csdu.net/ztl/images/shandian.png" class="lightning-icon"></image>
          </view>
          <view class="price-content">
            <view class="original-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">158</view>
              </view>
              <view class="price-label">原价</view>
            </view>
            <view class="member-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">88</view>
              </view>
              <view class="price-label">会员价</view>
            </view>
            <view class="grab-text">抢</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 门店卡片4 -->
    <view class="store-card">
      <view class="store-card-content">
        <view class="store-image"></view>
        <view class="store-info">
          <text class="store-name">国腾汽车服务中心</text>
          <view class="store-details">
            <view class="rating">
              <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
              <text class="rating-text">4.99</text>
            </view>
            <view class="status-open">
              <text class="status-text">营业中</text>
              <view class="time-badge">
                <text class="time-text">10:00-21:00</text>
              </view>
            </view>
            <view class="distance">
              <image src="https://oss.csdu.net/ztl/images/dingweibai.png" class="location-icon"></image>
              <text class="distance-text">3.6km</text>
            </view>
          </view>
          <text class="store-address">龙华区同胜社区华繁路1-10号</text>
        </view>
      </view>
      
      <!-- 服务项目信息 - 只有在服务类型被选中时显示 -->
      <view class="section_2 {{selectedService ? '' : 'hidden'}}">
        <view class="block_2">
          <text lines="1" class="text_6">标准洗车-五座轿车</text>
          <view class="text-wrapper_3">
            <text lines="1" class="text_7">3小时内可服务</text>
          </view>
        </view>
        <view class="price-container" bindtap="onGrabTap" data-store="国腾汽车服务中心" data-price="78" data-original="138">
          <view class="price-bg">
            <view class="price-gradient"></view>
            <image src="https://oss.csdu.net/ztl/images/shandian.png" class="lightning-icon"></image>
          </view>
          <view class="price-content">
            <view class="original-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">138</view>
              </view>
              <view class="price-label">原价</view>
            </view>
            <view class="member-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">78</view>
              </view>
              <view class="price-label">会员价</view>
            </view>
            <view class="grab-text">抢</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 门店卡片5 -->
    <view class="store-card">
      <view class="store-card-content">
        <view class="store-image"></view>
        <view class="store-info">
          <text class="store-name">国腾汽车服务中心</text>
          <view class="store-details">
            <view class="rating">
              <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
              <text class="rating-text">4.99</text>
            </view>
            <view class="status-open">
              <text class="status-text">营业中</text>
              <view class="time-badge">
                <text class="time-text">10:00-21:00</text>
              </view>
            </view>
            <view class="distance">
              <image src="https://oss.csdu.net/ztl/images/dingweibai.png" class="location-icon"></image>
              <text class="distance-text">3.6km</text>
            </view>
          </view>
          <text class="store-address">龙华区同胜社区华繁路1-10号</text>
        </view>
      </view>
      
      <!-- 服务项目信息 - 只有在服务类型被选中时显示 -->
      <view class="section_2 {{selectedService ? '' : 'hidden'}}">
        <view class="block_2">
          <text lines="1" class="text_6">标准洗车-五座轿车</text>
          <view class="text-wrapper_3">
            <text lines="1" class="text_7">3小时内可服务</text>
          </view>
        </view>
        <view class="price-container" bindtap="onGrabTap" data-store="国腾汽车服务中心" data-price="98" data-original="168">
          <view class="price-bg">
            <view class="price-gradient"></view>
            <image src="https://oss.csdu.net/ztl/images/shandian.png" class="lightning-icon"></image>
          </view>
          <view class="price-content">
            <view class="original-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">168</view>
              </view>
              <view class="price-label">原价</view>
            </view>
            <view class="member-price">
              <view class="price-row">
                <view class="price-symbol">￥</view>
                <view class="price-number">98</view>
              </view>
              <view class="price-label">会员价</view>
            </view>
            <view class="grab-text">抢</view>
          </view>
        </view>
      </view>
    </view>
  </view>
  </scroll-view>
  
  <!-- 抢购组件 -->
  <qianggou wx:if="{{showQianggou}}" show="{{showQianggou}}" storeData="{{currentGrabData}}" bind:close="onQianggouClose" bind:pay="onQianggouPay"></qianggou>
</view> 