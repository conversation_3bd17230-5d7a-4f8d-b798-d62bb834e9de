/**
 * 通用导航工具函数
 */

/**
 * 返回上一页
 * @param {number} delta 返回的页面数，默认为1
 */
export function navigateBack(delta = 1) {
  wx.navigateBack({
    delta: delta
  })
}

/**
 * 跳转到指定页面
 * @param {string} url 页面路径
 * @param {object} params 页面参数
 */
export function navigateTo(url, params = {}) {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  wx.navigateTo({
    url: fullUrl
  })
}

/**
 * 重定向到指定页面
 * @param {string} url 页面路径
 * @param {object} params 页面参数
 */
export function redirectTo(url, params = {}) {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  wx.redirectTo({
    url: fullUrl
  })
}

/**
 * 重新启动到指定页面
 * @param {string} url 页面路径
 * @param {object} params 页面参数
 */
export function reLaunch(url, params = {}) {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  wx.reLaunch({
    url: fullUrl
  })
}

/**
 * 切换到 tabBar 页面
 * @param {string} url tabBar 页面路径
 */
export function switchTab(url) {
  wx.switchTab({
    url: url
  })
} 