/* 页面基础样式 */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  object-fit: cover;
}


/* 页面标题样式 */
.page-header {
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  overflow-wrap: break-word;
  color: #ffffff;
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}

/* 新增图片样式 */
.top-image {
  display: block;
  margin: 40rpx 0 0 31rpx;
  width: 396rpx;
  height: 99rpx;
}

/* 服务特色说明样式 */
.service-features {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 14rpx 31rpx 0;
  padding: 0;
}

.feature-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}

.feature-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #ffffff;
  text-align: left;
  line-height: 16rpx;
}

.feature-item-1 {
  margin-right: 60rpx;
}

.feature-item-2 {
  margin-right: 57rpx;
}

.feature-item-3 {
  margin-right: 0;
}

/* 车辆信息卡片 */
.vehicle-card {
  width: 706rpx;
  height: 232rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 20rpx 22rpx 0 22rpx;
  background-color: #FFFFFF;
  border-radius: 25rpx;
  padding: 40rpx 26rpx 26rpx 26rpx;
  box-sizing: border-box;
}

.vehicle-header {
  width: 120rpx;
  height: 40rpx;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  z-index: 10;
}

.vehicle-label-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.vehicle-label {
  position: relative;
  z-index: 2;
  color: #FFFFFF;
  font-size: 18rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18rpx;
  max-width: 90rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8rpx 0 0 15rpx;
}

.vehicle-info {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 26rpx;
}

.vehicle-image {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.vehicle-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.vehicle-name {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 21rpx;
}

.car-brand {
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-right: 26rpx;
}

.edit-icon {
  width: 27rpx;
  height: 27rpx;
}

.car-model {
  overflow-wrap: break-word;
  color: rgba(139,139,139,1);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}

.license-plate {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.plate-number {
  background-color: rgba(86,38,162,1.000000);
  border-radius: 8rpx;
  width: 145rpx;
  height: 35rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.plate-prefix {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 23rpx;
  font-family: MicrosoftYaHei-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 23rpx;
  margin-left: 8rpx;
}

.plate-suffix {
  overflow-wrap: break-word;
  color: rgba(57,22,108,1);
  font-size: 23rpx;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 23rpx;
  margin-left: 6rpx;
}

.auth-status {
  background-color: rgba(225,227,234,1.000000);
  border-radius: 8rpx;
  height: 35rpx;
  width: 98rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-text {
  overflow-wrap: break-word;
  color: rgba(62,65,88,1);
  font-size: 23rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 23rpx;
}

/* 预约信息表单 */
.appointment-form {
  background: #ffffff;
  margin: 16rpx 32rpx 32rpx 22rpx;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.form-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.form-icon {
  width: 40rpx;
  height: 40rpx;
}

.form-title {
  color: #333333;
  font-size: 32rpx;
  font-weight: 600;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  color: #333333;
  font-size: 32rpx;
}

.form-value {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.form-placeholder {
  color: #999999;
  font-size: 32rpx;
}

.form-selected {
  color: #333333;
  font-size: 32rpx;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 底部支付按钮 */
.payment-section {
  padding: 32rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.payment-button {
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 35rpx;
  padding: 24rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(63, 103, 255, 0.2);
}

.payment-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}