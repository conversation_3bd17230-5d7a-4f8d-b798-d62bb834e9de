// 简化版登录页面用于测试
Page({
  data: {
    statusBarHeight: 44,
    isLoading: false
  },

  onLoad(options) {
    console.log('登录页面加载', options);
    const statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
    this.setData({ statusBarHeight });
  },

  handleGetPhoneNumber(e) {
    console.log('获取手机号回调:', e);
    wx.showToast({
      title: '功能测试中',
      icon: 'none'
    });
  },

  showPrivacyPolicy() {
    wx.showModal({
      title: '隐私政策',
      content: '隐私政策内容',
      showCancel: false
    });
  },

  showUserAgreement() {
    wx.showModal({
      title: '用户协议', 
      content: '用户协议内容',
      showCancel: false
    });
  }
});