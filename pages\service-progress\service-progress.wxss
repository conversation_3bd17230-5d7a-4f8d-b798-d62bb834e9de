/* 页面基础样式 */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f5f5 0%, #ffffff 100%);
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #ffffff;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 49rpx;
  position: relative;
}

.navbar-left-group {
  display: flex;
  align-items: center;
  position: absolute;
  left: 49rpx;
}

.back-btn {
  width: 40rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
}

.title-icon-wrapper {
  width: 40rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-icon {
  width: 32rpx;
  height: 33rpx;
}

.navbar-title {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.title-text {
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
}

.navbar-right {
  width: 60rpx;
  height: 60rpx;
  position: absolute;
  right: 49rpx;
}

/* 页面内容 */
.page-content {
  padding: 0 0rpx 20rpx 0rpx;
  flex: 1;
}

/* 门店信息卡片 */
.store-card {
  height: 142rpx;
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.store-info {
  display: flex;
  align-items: flex-start;
  flex: 1;
  padding-top: 20rpx;
}

.store-icon {
  width: 30rpx;
  height: 28rpx;
  margin-right: 20rpx;
}

.store-details {
  flex: 1;
}

.store-name {
  color: rgba(30,30,30,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: bold;
  line-height: 31rpx;
  margin-bottom: 10rpx;
  display: block;
}

.store-address {
  display: flex;
  align-items: center;
}

.address-text {
  color: rgba(52, 52, 52, 1);
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  font-weight: normal;
  line-height: 24rpx;
}

.address-suffix {
  color: rgba(52, 52, 52, 1);
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  font-weight: normal;
  line-height: 24rpx;
  margin-left: 5rpx;
}

.store-arrow {
  width: 47rpx;
  height: 47rpx;
}

/* 进度标题和刷新 */
.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 31rpx 21rpx 20rpx 29rpx;
}

.progress-title {
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  line-height: 32rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 15rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
}

.refresh-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}

.refresh-text {
  color: rgba(37,32,39,1);
  font-size: 24rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 24rpx;
}

/* 进度时间线 */
.progress-timeline {
  display: flex;
  margin: 31rpx 30rpx 20rpx 29rpx;
}

.timeline-left {
  width: rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 16rpx;
}

.timeline-start {
  width: 45rpx;
  height: 45rpx;
  margin-bottom: 10rpx;
}

.timeline-line {
  width: 5rpx;
  background: linear-gradient(180deg, #9e93ff 0%, #9e93ff 100%);
  margin: 10rpx 0;
}

.timeline-line-1 { height: 187rpx; }
.timeline-line-2 { height: 106rpx; }
.timeline-line-3 { height: 106rpx; }
.timeline-line-4 { height: 106rpx; }
.timeline-line-5 { height: 106rpx; }

.timeline-dot {
  width: 29rpx;
  height: 29rpx;
  border-radius: 50%;
  background-color: #ffffff;
  border: 4rpx solid rgba(158,147,255,1);
  margin: 10rpx 0;
}

.timeline-dot-1, .timeline-dot-2 { border-color: #9e93ff; }
.timeline-dot-3 { border-color: #9e93ff; background-color: #9e93ff; }
.timeline-dot-4, .timeline-dot-5 { border-color: #e0e0e0; }

.timeline-content {
  flex: 1;
}

/* 预约信息卡片 */
.appointment-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.card-title {
  color: rgba(89,47,167,1);
  font-size: 32rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  line-height: 32rpx;
  margin-bottom: 15rpx;
  display: block;
}

.appointment-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.time-text {
  color: rgba(34,40,52,1);
  font-size: 28rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  line-height: 28rpx;
}

.modify-btn {
  border: 2rpx solid rgba(62,73,92,1);
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modify-text {
  color: rgba(50,62,82,1);
  font-size: 23rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 23rpx;
}

.appointment-note {
  color: rgba(131,131,131,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 30rpx;
  display: block;
}

/* 服务步骤卡片 */
.service-steps {
  display: flex;
  flex-direction: column;
}

.step-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.step-completed {
  /* 已完成状态，保持默认样式 */
}

.step-current {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.step-pending {
  opacity: 0.7;
}

.step-title {
  color: rgba(42,54,75,1);
  font-size: 32rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  line-height: 32rpx;
  margin-bottom: 10rpx;
  display: block;
}

.step-desc {
  color: rgba(131,131,131,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 30rpx;
  display: block;
}

/* 服务前后对比 */
.comparison-card {
  background-color: #ffffff;
  border-radius: 15rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.comparison-title {
  color: rgba(42,54,75,1);
  font-size: 32rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  line-height: 32rpx;
  margin-bottom: 20rpx;
  display: block;
}

.comparison-section {
  margin-bottom: 20rpx;
}

.section-label {
  color: rgba(131,131,131,1);
  font-size: 22rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 30rpx;
  margin-bottom: 15rpx;
  display: block;
}

.comparison-images {
  display: flex;
}

.comparison-item {
  width: 140rpx;
  height: 140rpx;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.before-item {
  background-color: rgba(64,64,64,1);
}

.after-item {
  background-color: rgba(64,64,64,1);
}

.score-item {
  background: linear-gradient(135deg, #9e93ff 0%, #7c6fff 100%);
}

.score-text {
  color: rgba(255,255,255,1);
  font-size: 36rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 36rpx;
} 