.mine-bg {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  background: #f7f8fa;
  overflow-x: hidden;
}
.mine-bg-img {
  position: absolute;
  left: 0;
  top: 0;
  width: 100vw;
  height: 420rpx;
  z-index: 0;
}
.mine-content {
  position: relative;
  z-index: 1;
  padding: 0 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  width: 100vw;
}

.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-align-center {
  align-items: center;
}
.flex-justify-between {
  justify-content: space-between;
}

.mine-user {
  margin-top: 64rpx;
  margin-bottom: 32rpx;
}
.avatar {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.06);
}
.user-info {
  margin-left: 32rpx;
}
.nickname {
  font-size: 36rpx;
  color: #222;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.vip-badge {
  background: linear-gradient(90deg, #3F67FF 0%, #5C299E 100%);
  border-radius: 20rpx;
  padding: 0 0 0 0;
  height: 36rpx;
  display: flex;
  align-items: center;
  width: fit-content;
}
.vip-text {
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  margin-left: 8rpx;
  margin-right: 12rpx;
}
.vip-icon {
  width: 55rpx;
  height: 55rpx;
  margin-left: 0;
}

.mine-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.03);
  width: 706rpx;
  height: 245rpx;
  box-sizing: border-box;
}

.vip-card {
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.03);
  width: 706rpx;
  height: 262rpx;
  box-sizing: border-box;
  position: relative;
  overflow: visible;
}

.vip-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 100%;
  background: linear-gradient(to right, transparent, #4A4A8A);
  z-index: 10;
  pointer-events: none;
  border-top-right-radius: 24rpx;
  border-bottom-right-radius: 24rpx;
}
.vip-card-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.card-header {
  margin-bottom: 16rpx;
  position: relative;
  z-index: 70;
}
.card-title-section {
  display: flex;
  flex-direction: column;
  position: relative;
  margin-left: 23rpx;
}
.card-title-img {
  position: absolute;
  top: -6rpx;
  left: 0;
  width: 214rpx;
  height: 53rpx;
}
.title-divider {
  height: 0.5rpx;
  background-color: #e0e0e0;
  opacity: 0.3;
  position: absolute;
  top: 69rpx;
  left: 24rpx;
  right: 28rpx;
}
.card-main-title {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.card-savings-container {
  position: absolute;
  top: 20rpx;
  left: 279rpx;
  display: flex;
  align-items: center;
  z-index: 60;
}
.card-savings {
  font-size: 28rpx;
  color: #ECE2FF;
  white-space: nowrap;
  margin-right: 16rpx;
  font-weight: 500;
}
.card-savings-icon {
  width: 204rpx;
  height: 122rpx;
  margin-top: -66rpx;
  z-index: 200;
}
.card-logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 13rpx;
  background: linear-gradient(90deg, #B5E5FF, #9CC5FF, #C6C0FE);
  border-radius: 20rpx;
  width: 131rpx;
  height: 40rpx;
  z-index: 25;
  position: relative;
}
.card-logo {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}
.activate-btn {
  color: #000000;
  font-size: 24rpx;
  font-weight: bold;
}
.card-categories {
  margin-bottom: 13rpx;
  margin-top: 18rpx;
  margin-left: 22rpx;
  position: relative;
  z-index: 1;
}
.categories-text {
  font-size: 24rpx;
  color: #95BAFF;
  font-weight: 500;
  height: 25rpx;
  line-height: 25rpx;
}
.card-title {
  font-size: 28rpx;
  color: #222;
  font-weight: 600;
  margin-bottom: 24rpx;
}
.card-title-text {
  font-size: 28rpx;
  color: #222;
  font-weight: 600;
}
.card-arrow {
  width: 11rpx;
  height: 20rpx;
  margin-left: 8rpx;
  filter: brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0.6) contrast(1);
}
.card-more {
  color: #8d8d8d;
  font-size: 24rpx;
  margin-right: 8rpx;
}
.card-benefits {
  width: calc(100% - 30rpx);
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: flex-start;
  gap: 16rpx;
  margin-left: 22rpx;
  overflow: visible;
}
.benefit-item {
  width: 190rpx;
  height: 76rpx;
  background: linear-gradient(90deg, #D3D7FF, #F5F4FF);
  border-radius: 12rpx;
  padding: 16rpx 16rpx 16rpx 16rpx;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  flex-shrink: 0;
  position: relative;
}
.benefit-main {
  font-size: 24rpx;
  color: #323474;
  font-weight: bold;
  margin-bottom: 12rpx;
  height: 23rpx;
  line-height: 23rpx;
}
.benefit-sub {
  font-size: 18rpx;
  color: #7A62B8;
  font-weight: 500;
  height: 17rpx;
  line-height: 17rpx;
}

.benefit-bg-img-1,
.benefit-bg-img-2,
.benefit-bg-img-3,
.benefit-bg-img-4 {
  position: absolute;
  right: 16rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  z-index: 2;
  pointer-events: none;
}

.mine-assets {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.03);
  width: 706rpx;
  height: 135rpx;
  box-sizing: border-box;
}
.asset-item {
  width: 33%;
  text-align: center;
}
.asset-value {
  font-size: 28rpx;
  color: #080808;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.asset-label {
  font-size: 24rpx;
  color: #080808;
  font-weight: 500;
}

.order-status-list {
  margin-top: 24rpx;
}
.order-status-item {
  width: 25%;
  text-align: center;
}
.order-status-icon {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 8rpx;
}
.order-status-text {
  font-size: 22rpx;
  color: #222;
}

.car-card {
  margin-bottom: 32rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 24rpx 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.03);
  width: 706rpx;
  height: 184rpx;
  box-sizing: border-box;
  position: relative;
}
.car-icon-container {
  position: absolute;
  top: 36rpx;
  left: 24rpx;
  width: 70rpx;
  height: 70rpx;
  background-color: #D1D5DF;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.car-icon {
  width: 43rpx;
  height: 43rpx;
}
.car-info {
  flex: 1;
  margin-left: 110rpx;
}
.car-add {
  font-size: 32rpx;
  color: #252027;
  font-weight: 900;
  margin-bottom: 8rpx;
  position: absolute;
  top: 34rpx;
  left: 118rpx;
}
.car-model {
  font-size: 24rpx;
  color: #8d8d8d;
  font-weight: 500;
  position: absolute;
  top: 80rpx;
  left: 118rpx;
}
.car-arrow {
  position: absolute;
  right: 24rpx;
  top: 34rpx;
  width: 11rpx;
  height: 20rpx;
  filter: brightness(0) saturate(100%) invert(60%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0.6) contrast(1);
}

.mine-func-list {
  margin-top: 16rpx;
  margin-bottom: 32rpx;
  margin-left: -10rpx;
  margin-right: -10rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.03);
  width: 706rpx;
  height: 165rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.func-item {
  width: 25%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.func-icon-1 {
  width: 85rpx;
  height: 65rpx;
  margin-bottom: 8rpx;
}

.func-icon-2 {
  width: 75rpx;
  height: 67rpx;
  margin-bottom: 8rpx;
}

.func-icon-3 {
  width: 49rpx;
  height: 45rpx;
  margin-bottom: 8rpx;
}

.func-icon-4 {
  width: 54rpx;
  height: 54rpx;
  margin-bottom: 8rpx;
}

/* 为每个功能项单独设置样式，确保文字对齐 */
.func-item:nth-child(1) .func-text {
  margin-top: 0;
}

.func-item:nth-child(2) .func-text {
  margin-top: 2rpx;
}

.func-item:nth-child(3) .func-text {
  margin-top: 20rpx;
}

.func-item:nth-child(4) .func-text {
  margin-top: 11rpx;
}
.func-text {
  font-size: 22rpx;
  color: #222;
  text-align: center;
  line-height: 1.2;
}

/* 去除顶部标题栏、状态栏、底部tabbar相关样式 */ 