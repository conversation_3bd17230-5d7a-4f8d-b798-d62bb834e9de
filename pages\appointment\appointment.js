// pages/appointment/appointment.js
Page({
  data: {
    statusBarHeight: 0,
    showSelectTime: false,
    selectedTime: '',
    selectedShop: null // 新增：用于存储选中的门店信息
  },

  onLoad: function (options) {
    console.info("页面加载");
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
  },

  onShow: function () {
    console.info("页面显示");
  },

  onHide: function () {
    console.info("页面隐藏");
  },

  onUnload: function () {
    console.info("页面卸载");
  },

  // 选择门店
  selectStore: function() {
    console.log('选择门店');
    wx.navigateTo({
      url: '/pages/select-shop/select-shop'
    });
  },

  // 设置选中的门店信息
  setSelectedShop: function(shopInfo) {
    console.log('设置选中的门店:', shopInfo);
    this.setData({
      selectedShop: shopInfo
    });
  },

  // 选择服务
  selectService: function() {
    console.log('选择服务');
    wx.navigateTo({
      url: '/pages/select-spu/select-spu'
    });
  },

  // 选择时间
  selectTime: function() {
    console.log('选择时间');
    this.setData({ showSelectTime: true });
  },

  // 关闭弹窗
  onSelectTimeClose: function() {
    this.setData({ showSelectTime: false });
  },

  // 选中时间回传
  onTimeSelected: function(e) {
    const { date, time } = e.detail;
    this.setData({
      selectedTime: `${date} ${time}`,
      showSelectTime: false
    });
    // 你可以在这里做进一步处理，比如显示到页面上
  },

  // 立即支付
  payNow: function() {
    console.log('立即支付');
    
    // 跳转到待支付页面
    wx.navigateTo({
      url: '/pages/wait-pay/wait-pay'
    });
  },


});