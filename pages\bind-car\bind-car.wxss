/* pages/bind-car/bind-car.wxss */

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1000;
  border-bottom: 1rpx solid #f0f0f0;
}

.navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
}

.navbar-left {
  position: absolute;
  left: 32rpx;
  display: flex;
  align-items: center;
  z-index: 10;
}

.nav-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.back-icon, .home-icon {
  width: 32rpx;
  height: 32rpx;
}

.navbar-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  width: auto;
  white-space: nowrap;
}

.navbar-right {
  position: absolute;
  right: 32rpx;
  width: 60rpx;
  z-index: 10;
}

.navbar-spacer {
  width: 100%;
}

/* 页面主体 */
.page {
  position: relative;
  min-height: 100vh;
}

.background-image {
  position: fixed;
  top: 114rpx;
  left: 0;
  width: 750rpx;
  height: 894rpx;
  object-fit: cover;
  z-index: -1; /* 确保背景图片在其他内容之下 */
}

/* 车牌输入区域 */
.plate-form-section {
  margin: 300rpx 24rpx 32rpx;
  position: relative;
  z-index: 3;
}

.form-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.form-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  text-align: left;
}

.form-hint {
  font-size: 26rpx;
  color: #999;
  text-align: left;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

/* 车牌输入框 */
.plate-input-container {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.plate-input-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.plate-input {
  width: 69rpx;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background: #fff;
  margin-right: 7rpx;
}

.plate-input.active {
  border-color: #667eea;
  background: #f0f4ff;
}

.plate-separator {
  width: 8rpx;
  height: 8rpx;
  background-color: #333;
  border-radius: 50%;
  margin: 0 8rpx;
  display: inline-block;
}

.new-energy-box {
  width: 120rpx;
  height: 80rpx;
  border: 2rpx dashed #52c41a;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 16rpx;
}

.new-energy-text {
  font-size: 22rpx;
  color: #52c41a;
  text-align: center;
}

/* 用户协议 */
.agreement-section {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 0;
}

.agreement-checkbox {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox-icon {
  width: 32rpx;
  height: 32rpx;
}

.checkbox-empty {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #ddd;
  border-radius: 4rpx;
}

.agreement-text {
  font-size: 28rpx;
  color: #666;
  text-align: left;
}

.agreement-link {
  font-size: 28rpx;
  color: #ff4757;
  text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.submit-btn.disabled {
  background: #f0f0f0;
  color: #999;
}

/* 其他添加方式 */
.alternative-section {
  padding: 0 24rpx 48rpx;
}

.alternative-title {
  display: block;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.alternative-btn {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.btn-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.btn-arrow {
  width: 16rpx;
  height: 30rpx;
  color: #130F35;
}

.btn-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.accuracy-tag {
  width: 194rpx;
  height: 37rpx;
  background: linear-gradient(90deg, #3F67FF, #5C299E);
  border-radius: 19rpx 19rpx 19rpx 0rpx;
  padding: 4rpx 12rpx;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-text {
  font-size: 22rpx;
  color: #ffffff;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
} 