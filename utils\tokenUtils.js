/**
 * 统一的Token解析工具
 * 解决多个token管理器解析不一致的问题
 */

// 统一的Base64解码方法（小程序环境优化版本）
const decodeBase64 = (str) => {
  try {
    if (!str) return '';
    
    // 添加padding如果需要
    const paddedStr = str + '='.repeat((4 - str.length % 4) % 4);
    
    // 小程序环境使用手动解码
    return manualBase64Decode(paddedStr);
  } catch (error) {
    console.error('Base64解码失败:', error);
    return '';
  }
};

// 手动Base64解码（小程序环境适用）
const manualBase64Decode = (str) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  let result = '';
  str = str.replace(/[^A-Za-z0-9+/]/g, '');
  
  for (let i = 0; i < str.length; i += 4) {
    const encoded1 = chars.indexOf(str.charAt(i));
    const encoded2 = chars.indexOf(str.charAt(i + 1));
    const encoded3 = chars.indexOf(str.charAt(i + 2));
    const encoded4 = chars.indexOf(str.charAt(i + 3));
    
    const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;
    
    result += String.fromCharCode((bitmap >> 16) & 255);
    if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
    if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
  }
  
  return result;
};

// 统一的JWT Token解析方法
const parseJWTToken = (token) => {
  try {
    // 检查token是否为空或无效
    if (!token || typeof token !== 'string') {
      console.warn('Token为空或格式无效');
      return null;
    }
    
    // JWT token格式: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('JWT token格式错误，期望3个部分，实际:', parts.length);
      return null;
    }
    
    // 解码payload部分
    const payload = parts[1];
    if (!payload) {
      console.warn('JWT payload为空');
      return null;
    }
    
    // 使用统一的Base64解码
    const decodedPayload = decodeBase64(payload);
    if (!decodedPayload) {
      console.warn('解码后的payload为空');
      return null;
    }
    
    // 使用正则表达式提取信息（更可靠的方法）
    const userMatch = decodedPayload.match(/"user_id":(\d+)/);
    const expiredMatch = decodedPayload.match(/"expired":(\d+)/);
    
    if (userMatch && expiredMatch) {
      const userInfo = {
        user_id: parseInt(userMatch[1]),
        expired: parseInt(expiredMatch[1])
      };
      
      return {
        user_id: userInfo.user_id,
        expired: userInfo.expired,
        isLogin: true
      };
    } else {
      console.warn('无法从payload中提取user_id或expired');
      return null;
    }
  } catch (error) {
    console.error('解析JWT token失败:', error);
    // 返回null而不是抛出错误，避免影响其他功能
    return null;
  }
};

// 检查token是否过期
const isTokenExpired = (token) => {
  try {
    const tokenInfo = parseJWTToken(token);
    if (!tokenInfo || !tokenInfo.expired) {
      return true; // 无法解析的token认为已过期
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= tokenInfo.expired;
  } catch (error) {
    console.error('检查token过期失败:', error);
    return true; // 出错时认为已过期
  }
};

// 获取token剩余时间（秒）
const getTokenTimeLeft = (token) => {
  try {
    const tokenInfo = parseJWTToken(token);
    if (!tokenInfo || !tokenInfo.expired) {
      return 0;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    return Math.max(0, tokenInfo.expired - currentTime);
  } catch (error) {
    console.error('获取token剩余时间失败:', error);
    return 0;
  }
};

module.exports = {
  parseJWTToken,
  isTokenExpired,
  getTokenTimeLeft,
  decodeBase64
}; 