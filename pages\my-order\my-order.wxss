/* 页面整体样式 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部导航栏 */
.header {
  background-color: #ffffff;
  height: 88rpx; /* 调整为微信默认导航栏高度 */
  position: relative;
  z-index: 100;
}

.header-content {
  position: relative;
  height: 88rpx; /* 微信默认导航栏高度 */
  width: 100%;
}

.back-icon {
  width: 15rpx;
  height: 28rpx;
  position: absolute;
  left: 49rpx;
  top: 50%;
  transform: translateY(-50%);
}

.more-icon {
  width: 32rpx;
  height: 33rpx;
  position: absolute;
  right: 49rpx;
  top: 50%;
  transform: translateY(-50%);
}

.header-title {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 34rpx;
  font-weight: 600;
  color: #000000;
  line-height: 34rpx;
}

/* 标签页样式 */
.tab-container {
  background-color: #ffffff;
  height: 100rpx;
  position: relative;
  margin-top: 70rpx; /* 添加顶部间距，与导航栏分开 */
  margin-bottom: 20rpx;
}

.tab-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 52rpx;
}

.tab-item {
  text-align: center;
  position: relative;
  flex: 1;
}

.tab-text {
  font-size: 28rpx;
  color: #3c3c3c;
  line-height: 28rpx;
  transition: color 0.3s;
}

.tab-item.active .tab-text {
  color: #5B2FA5;
  font-weight: bold;
}

.tab-indicator {
  position: absolute;
  bottom: 15rpx;
  width: 50rpx;
  height: 6rpx;
  background: linear-gradient(135deg, #3F67FF 0%, #5C299E 100%);
  border-radius: 3rpx;
  transition: left 0.3s ease-in-out;
  transform: translateX(-50%); /* 让指示器以自身中心对齐 */
}



/* 订单列表 */
.order-list {
  padding: 0 23rpx;
}

.order-item {
  background-color: #ffffff;
  border-radius: 25rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

/* 订单头部 */
.order-header {
  display: flex;
  align-items: center;
  padding: 25rpx 24rpx 0 24rpx;
  height: 31rpx;
}

.shop-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.shop-icon {
  width: 30rpx;
  height: 28rpx;
  margin-right: 8rpx;
}

.shop-name {
  font-size: 32rpx;
  color: #1e1e1e;
  font-weight: bold;
  line-height: 32rpx;
}

.order-status {
  font-size: 24rpx;
  font-weight: bold;
  margin-left: auto;
  margin-right: 16rpx;
}

.status-icon {
  width: 1rpx;
  height: 25rpx;
  margin-right: 16rpx;
}

.arrow-icon {
  width: 23rpx;
  height: 25rpx;
}

/* 服务信息 */
.service-info {
  display: flex;
  padding: 23rpx 15rpx 0 15rpx;
  height: 156rpx;
}

.service-image {
  width: 156rpx;
  height: 156rpx;
  background-color: #6677b3;
  border-radius: 25rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.service-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 14rpx 0;
}

.service-name {
  font-size: 28rpx;
  color: #121f2b;
  font-weight: 600;
  line-height: 28rpx;
}

.service-desc {
  font-size: 22rpx;
  color: #838383;
  line-height: 22rpx;
  margin-top: 24rpx;
}

.price-container {
  display: flex;
  align-items: baseline;
  margin-top: 22rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4065;
  font-weight: 700;
  line-height: 24rpx;
}

.price-value {
  font-size: 42rpx;
  color: #ff4065;
  font-weight: 700;
  line-height: 42rpx;
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 13rpx 0;
  margin-right: 24rpx;
}

.amount-text {
  font-size: 26rpx;
  color: #030303;
  font-weight: 500;
  margin-right: 10rpx;
}

.amount-value {
  font-size: 26rpx;
  color: #030303;
  font-weight: bold;
}

/* 预约时间 */
.appointment-time {
  display: flex;
  align-items: center;
  background-color: #ffecf7;
  border-radius: 3rpx;
  padding: 21rpx 18rpx;
  margin: 19rpx 16rpx;
}

.time-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.time-label {
  font-size: 24rpx;
  color: #000b18;
  font-weight: bold;
}

.time-value {
  font-size: 24rpx;
  color: #4a566f;
  font-weight: bold;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 15rpx 16rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 19rpx 24rpx 23rpx 24rpx;
  gap: 16rpx;
}

.btn {
  height: 58rpx;
  border-radius: 29rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 168rpx;
}

.btn-outline {
  border: 2rpx solid #3e495c;
  background-color: transparent;
}

.btn-primary {
  background: linear-gradient(135deg, #3F67FF 0%, #5C299E 100%);
  border: none;
}

.btn-text {
  font-size: 24rpx;
  font-weight: bold;
  line-height: 24rpx;
}

.btn-outline .btn-text {
  color: #323e52;
}

.btn-primary .btn-text {
  color: #ffffff;
}

/* 底部提示样式 */
.bottom-tip {
  padding: 40rpx 0 60rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.divider-line {
  width: 200rpx;
  height: 1rpx;
  background-color: #e5e5e5;
  margin-bottom: 20rpx;
}

.no-more-text {
  font-size: 24rpx;
  color: #999999;
  line-height: 24rpx;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .action-buttons {
    flex-direction: column;
    align-items: flex-end;
  }
  
  .btn {
    width: 168rpx;
  }
} 