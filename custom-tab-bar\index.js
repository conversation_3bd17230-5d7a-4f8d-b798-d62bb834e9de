Component({
  data: {
    selected: 0,
    list: [
      {
        pagePath: "pages/home/<USER>",
        iconPath: "icon/sy.png",
        selectedIconPath: "icon/sy1.png",
        text: "首页"
      },
      {
        pagePath: "pages/service/service",
        iconPath: "icon/fw.png",
        selectedIconPath: "icon/fw1.png",
        text: "服务"
      },
      {
        pagePath: "pages/appointment/appointment",
        iconPath: "icon/yy.png",
        selectedIconPath: "icon/yy.png",
        text: "预约",
        isSpecial: true
      },
      {
        pagePath: "pages/mall/mall",
        iconPath: "icon/md.png",
        selectedIconPath: "icon/md1.png",
        text: "商城"
      },
      {
        pagePath: "pages/mine/mine",
        iconPath: "icon/wd.png",
        selectedIconPath: "icon/wd1.png",
        text: "我的"
      }
    ]
  },
  
  lifetimes: {
    attached() {
      console.log('=== 自定义tabbar组件已加载 ===');
      console.log('当前选中:', this.data.selected);
      console.log('tab列表:', this.data.list);
    }
  },
  
  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset;
      const url = data.path;
      console.log('切换到页面:', url, '索引:', data.index);
      wx.switchTab({
        url
      });
      this.setData({
        selected: data.index
      });
    }
  }
}); 