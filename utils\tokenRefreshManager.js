import request from './request.js';
import tokenManager from './tokenManager.js';

class TokenRefreshManager {
  constructor() {
    this.refreshPromise = null;
    this.isRefreshing = false;
  }

  // 智能刷新策略
  async refreshIfNeeded() {
    // 如果token还有5分钟以上有效期，不需要刷新
    if (!tokenManager.shouldRefresh()) {
      return true;
    }

    // 如果正在刷新，等待刷新完成
    if (this.isRefreshing) {
      return this.refreshPromise;
    }

    // 开始刷新
    this.isRefreshing = true;
    this.refreshPromise = this._doRefresh();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  // 执行刷新
  async _doRefresh() {
    const token = tokenManager.getToken();
    
    if (!token) {
      throw new Error('No token to refresh');
    }

    try {
      const response = await request({
        url: '/auth/refresh',
        method: 'POST',
        data: {}, // 空请求体
        header: {
          'Authorization': `Bearer ${token}` // token在请求头中
        }
      });

      if (response.code === 200) {
        // 更新token，86400秒过期
        tokenManager.setToken(response.data.token, 86400);
        return true;
      } else {
        throw new Error(response.message || 'Refresh failed');
      }
    } catch (error) {
      // 刷新失败，清除token
      tokenManager.clearToken();
      throw error;
    }
  }

  // 检查是否需要刷新
  shouldRefresh() {
    return tokenManager.shouldRefresh();
  }
}

// 创建全局实例
const tokenRefreshManager = new TokenRefreshManager();

export default tokenRefreshManager; 