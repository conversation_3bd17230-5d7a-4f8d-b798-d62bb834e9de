Page({
  data: {
    statusBarHeight: 44, // 默认值，动态获取
    // 添加服务类型选中状态管理
    selectedService: null, // 默认不选中任何服务
    serviceTypes: [
      { id: 'baoyang', name: '保养', selected: false },
      { id: 'xiche', name: '洗车', selected: false },
      { id: 'meirong', name: '美容', selected: false },
      { id: 'weixiu', name: '维修', selected: false }
    ],
    // 抢购相关状态
    showQianggou: false,
    currentGrabData: {}
  },
  
  onLoad() {
    const statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
    this.setData({ statusBarHeight });
  },
  
  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      });
    }
  },
  
  // 添加服务类型点击切换方法
  onServiceTap(e) {
    const serviceId = e.currentTarget.dataset.id;
    const serviceTypes = this.data.serviceTypes.map(item => ({
      ...item,
      selected: item.id === serviceId
    }));
    
    this.setData({
      selectedService: serviceId,
      serviceTypes: serviceTypes
    });
  },
  
  onUnload() {
    // 页面卸载
  },
  
  // 抢购相关方法
  onGrabTap(e) {
    const { store, price, original } = e.currentTarget.dataset;
    
    this.setData({
      showQianggou: true,
      currentGrabData: {
        store: store,
        price: price,
        original: original
      },
      pageScrollDisabled: true
    });
  },
  
  onQianggouClose() {
    this.setData({
      showQianggou: false,
      currentGrabData: {},
      pageScrollDisabled: false
    });
  },
  
  onQianggouPay(e) {
    const { price } = e.detail;
    
    // 这里可以添加支付逻辑
    wx.showToast({
      title: `支付￥${price}`,
      icon: 'success',
      duration: 2000
    });
    
    // 支付后关闭弹窗
    this.setData({
      showQianggou: false,
      currentGrabData: {},
      pageScrollDisabled: false
    });
  }
}); 