.page {
  position: relative;
  width: 750rpx;
  height: 100vh;
  background: linear-gradient(to bottom, #ffffff 0%, #e8e9f0 100%);
  display: flex;
  flex-direction: column;
}

/* 禁止页面滚动 */
.page-scroll-disabled {
  overflow: hidden !important;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

/* 固定头部区域 */
.fixed-header {
  flex-shrink: 0;
  background: linear-gradient(to bottom, #ffffff 0%, #e8e9f0 100%);
  z-index: 10;
}

/* 滚动内容区域 */
.scroll-content {
  flex: 1;
  height: 0;
}

/* 页面标题样式 */
.page-header {
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent;
  position: relative;
}

.page-title {
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
}

/* 搜索栏 */
.search-section {
  width: 706rpx;
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 25rpx;
  margin: 20rpx 22rpx 0 22rpx;
}

.search-bar {
  width: 557rpx;
  height: 68rpx;
  background-color: #F1F0F5;
  border-radius: 34rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 25rpx;
  margin-right: 28rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-placeholder {
  width: 200rpx;
  height: 68rpx;
  color: rgba(153,153,153,1);
  font-size: 26rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 68rpx;
  white-space: nowrap;
}

.map-btn {
  width: 121rpx;
  height: 68rpx;
  background-color: #F1F0F5;
  border-radius: 34rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.map-text {
  width: 48rpx;
  height: 68rpx;
  color: rgba(0,0,0,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: center;
  line-height: 68rpx;
}

.map-dots {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 8rpx;
}

.map-dots-img {
  width: 24rpx;
  height: 24rpx;
}

/* 服务类型筛选 */
.service-filters {
  width: 704rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 35rpx 0 0 20rpx;
}

.box_9 {
  background-color: rgba(226,224,244,1.000000);
  border-radius: 25rpx;
  position: relative;
  width: 169rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
}

.text-wrapper_1 {
  width: 115rpx;
  height: 51rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 21rpx 0 0 20rpx;
}

.text_9 {
  width: 54rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(30,30,30,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}

.text_10 {
  width: 114rpx;
  height: 15rpx;
  overflow-wrap: break-word;
  color: rgba(161,170,190,1);
  font-size: 18rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 9rpx 0 0 1rpx;
}

.image_11 {
  width: 83rpx;
  height: 78rpx;
  margin: 12rpx 1rpx 0 -50rpx;
}

.image_12 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 169rpx;
  height: 90rpx;
}



.image-wrapper_4 {
  height: 90rpx;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKkAAABaCAYAAADD03aYAAAACXBIWXMAAAsTAAALEwEAmpwYAAAHMWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78i 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) 100% no-repeat;
  background-size: 100% 100%;
  margin-left: 9rpx;
  display: flex;
  flex-direction: column;
  width: 169rpx;
}



/* 筛选选项 */
.filter-options {
  width: 750rpx;
  height: 23rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 34rpx 0 0 29rpx;
}

.filter-option {
  width: 109rpx;
  height: 23rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-right: 67rpx;
}

.filter-option:last-child {
  margin-right: 0;
}

.option-text {
  width: 93rpx;
  height: 23rpx;
  color: rgba(41,41,41,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 23rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.option-arrow {
  width: 10rpx;
  height: 6rpx;
}



/* 门店列表 */
.store-list {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  padding: 0 22rpx;
}

.store-card {
  width: 705rpx;
  min-height: 189rpx;
  background-color: rgba(255,255,255,1);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  margin: 19rpx 0 0 0;
}

.store-card-content {
  display: flex;
  flex-direction: row;
  height: 189rpx;
}

.store-image {
  width: 156rpx;
  height: 156rpx;
  background-color: rgba(102,119,179,1);
  border-radius: 25rpx;
  margin: 16rpx 22rpx 17rpx 16rpx;
}

.store-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding-top: 28rpx;
}

.store-name {
  width: 450rpx;
  height: 31rpx;
  color: rgba(30,30,30,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: 900;
  text-align: left;
  line-height: 31rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.store-details {
  width: 471rpx;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 20rpx;
}

.rating {
  width: 71rpx;
  height: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 13rpx;
}

.star-icon {
  width: 21rpx;
  height: 20rpx;
  margin-right: 4rpx;
}

.rating-text {
  width: 44rpx;
  height: 18rpx;
  color: rgba(226,61,93,1);
  font-size: 22rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  line-height: 18rpx;
}

.status-open {
  height: 32rpx;
  background-color: rgba(33,212,163,1);
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 4rpx;
  width: fit-content;
}

.status-closed {
  height: 32rpx;
  background-color: #FFA201;
  border-radius: 10rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 4rpx;
  width: fit-content;
}

.status-text {
  height: 20rpx;
  color: rgba(255,255,255,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 20rpx;
  margin: 0 13rpx 0 13rpx;
  white-space: nowrap;
}

.time-badge {
  height: 28rpx;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 12rpx;
  border-radius: 4rpx;
  width: fit-content;
  margin-left: 5rpx;
  margin-right: 4rpx;
}

.time-text {
  height: 18rpx;
  color: rgba(33,212,163,1);
  font-size: 22rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 18rpx;
  white-space: nowrap;
}

.status-closed .time-text {
  color: #FFA201;
}

.distance {
  width: 111rpx;
  height: 22rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: auto;
}

.location-icon {
  width: 19rpx;
  height: 22rpx;
  margin-right: 4rpx;
}

.distance-text {
  width: 80rpx;
  height: 19rpx;
  color: rgba(52,52,52,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 19rpx;
}

.store-address {
  width: 479rpx;
  height: 23rpx;
  color: rgba(52,52,52,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 23rpx;
  margin-top: 20rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 服务项目相关样式 */
.service-item {
  width: 169rpx;
  height: 90rpx;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

.service-image {
  width: 169rpx;
  height: 90rpx;
  position: relative;
  z-index: 10;
  pointer-events: auto;
}

.section_2 {
  width: 705rpx;
  height: 72rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 10rpx 0 27rpx 0;
  padding: 0;
}

.section_2.hidden {
  display: none;
}

.block_2 {
  width: 223rpx;
  height: 64rpx;
  margin: 6rpx 0 0 23rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.text_6 {
  width: 223rpx;
  height: 25rpx;
  overflow-wrap: break-word;
  color: rgba(18,31,43,1);
  font-size: 26rpx;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}

.text-wrapper_3 {
  border-radius: 3rpx;
  height: 28rpx;
  border: 1px solid rgba(33,212,163,1);
  margin-top: 11rpx;
  display: flex;
  flex-direction: column;
  width: 141rpx;
}

.text_7 {
  width: 129rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(0,169,151,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 4rpx 0 0 5rpx;
}

/* 新的价格容器样式 */
.price-container {
  position: relative;
  width: 374rpx;
  height: 73rpx;
  margin-right: 28rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.price-container:active {
  transform: scale(0.98);
}

.price-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 374rpx;
  height: 73rpx;
  z-index: 1;
  display: flex;
  border-radius: 0 36rpx 36rpx 0;
  overflow: hidden;
}

/* 左侧原价区域 - 斜切效果 */
.price-bg::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 175rpx;
  height: 100%;
  background: #E9E9F7;
  z-index: 1;
  clip-path: polygon(0 0, 100% 0, 89% 100%, 0 100%);
}


/* 右侧渐变背景 */
.price-gradient {
  position: absolute;
  left: 142rpx;
  top: 0;
  width: 251rpx;
  height: 100%;
  background: linear-gradient(90deg, #3F67FF,#5C299E);
  z-index: 1;
  clip-path: polygon(11% 0, 100% 0, 100% 100%, 0 100%);
  border-radius: 0 36rpx 36rpx 0;
}

/* 闪电图标样式 */
.lightning-icon {
  position: absolute;
  left: 140rpx;
  top: 0;
  width: 32rpx;
  height: 73rpx;
  z-index: 3;
}

.price-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0;
}

.member-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 120rpx;
  z-index: 3;
  margin-left: 16rpx;
}

.member-price .price-symbol,
.member-price .price-number {
  display: inline;
}

/* 会员价样式：保持现有颜色，使用原价的字重设置 */
.member-price .price-symbol {
  font-family: PingFang-SC-Bold, "Microsoft YaHei", sans-serif;
  font-weight: bold;
}

.member-price .price-number {
  font-family: PingFang-SC-Heavy, "Microsoft YaHei", sans-serif;
  font-weight: 900;
  font-size: 28rpx;
}

.member-price .price-label {
  font-family: PingFang-SC-Bold, "Microsoft YaHei", sans-serif;
  font-weight: bold;
}

.member-price .price-row {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
}

.price-symbol {
  color: rgba(253,253,253,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  line-height: 1;
}

.price-number {
  color: rgba(253,253,253,1);
  font-size: 36rpx;
  font-family: Impact;
  font-weight: normal;
  line-height: 1;
  margin: 0 0 0 2rpx;
}

.price-label {
  color: rgba(253,253,253,1);
  font-size: 20rpx;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  line-height: 1;
  margin-left: 4rpx;
}

.grab-text {
  color: rgba(253,253,253,1);
  font-size: 36rpx;
  font-family: PingFang-SC-Heavy, "Microsoft YaHei", sans-serif;
  font-weight: 900;
  font-style: italic;
  line-height: 1;
  z-index: 3;
  margin-right: 33rpx;
  padding: 20rpx;
  cursor: pointer;
}

.original-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 120rpx;
  z-index: 3;
}

.original-price .price-symbol,
.original-price .price-number {
  display: inline;
}

/* 原价样式：数字和符号红色，文字黑色 */
.original-price .price-number,
.original-price .price-symbol {
  color: rgba(226,61,93,1);
}

.original-price .price-symbol {
  font-family: PingFang-SC-Bold, "Microsoft YaHei", sans-serif;
  font-weight: bold;
}

.original-price .price-number {
  margin: 0 0 0 2rpx;
  font-family: PingFang-SC-Heavy, "Microsoft YaHei", sans-serif;
  font-weight: 900;
  font-size: 28rpx;
}

.original-price .price-label {
  color: rgba(0,0,0,1);
  margin-left: 4rpx;
  font-family: PingFang-SC-Bold, "Microsoft YaHei", sans-serif;
  font-weight: bold;
}

.original-price .price-row {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
} 