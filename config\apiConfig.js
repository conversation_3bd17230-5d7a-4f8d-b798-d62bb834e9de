// API接口分类配置
export const API_CONFIG = {
  // 必需登录的接口
  required: [
    '/user/profile',
    '/user/orders',
    '/user/vehicles',
    '/order/create',
    '/payment/process',
    '/user/bind-car',
    '/user/update-profile'
  ],
  
  // 可选登录的接口（有token显示用户数据，无token显示默认数据）
  optional: [
    '/public/info',
    '/products/list',
    '/service/list',
    '/shop/list',
    '/vehicle/default'
  ],
  
  // 公开接口（不需要token）
  public: [
    '/public/basic-info',
    '/public/announcements',
    '/public/config',
    '/spu/recommend',
    '/spu/list'
  ]
};

// 判断接口类型
export function getApiType(url) {
  if (API_CONFIG.required.includes(url)) {
    return 'required';
  } else if (API_CONFIG.optional.includes(url)) {
    return 'optional';
  } else {
    return 'public';
  }
}