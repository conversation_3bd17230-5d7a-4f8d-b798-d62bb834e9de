// 页面分类配置
export const PAGE_CONFIG = {
  // 必需登录的页面
  required: [
    'pages/mine/mine',
    'pages/my-order/my-order',
    'pages/bind-car/bind-car',
    'pages/wait-pay/wait-pay',
    'pages/service-progress/service-progress'
  ],
  
  // 可选登录的页面
  optional: [
    'pages/home/<USER>',
    'pages/mall/mall',
    'pages/service/service',
    'pages/shop-detail/shop-detail',
    'pages/select-spu/select-spu',
    'pages/select-shop/select-shop',
    'pages/select-time/select-time'
  ]
};

// 判断页面类型
export function getPageType(pagePath) {
  if (PAGE_CONFIG.required.includes(pagePath)) {
    return 'required';
  } else if (PAGE_CONFIG.optional.includes(pagePath)) {
    return 'optional';
  } else {
    return 'optional'; // 默认按可选处理
  }
} 