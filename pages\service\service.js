Page({
  data: {
    statusBarHeight: 44 // 默认值，动态获取
  },
  
  onLoad: function() {
    const statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
    this.setData({ statusBarHeight });
    console.log('服务页面加载成功');
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      });
    }
  },

  // 导航门店
  navigateToStore: function() {
    wx.showToast({
      title: '导航功能开发中',
      icon: 'none'
    });
  },

  // 预约服务
  bookService: function() {
    wx.navigateTo({
      url: '/pages/appointment/appointment'
    });
  },

  // 修改预约
  modifyBooking: function() {
    wx.showToast({
      title: '修改预约功能开发中',
      icon: 'none'
    });
  },

  // 查看服务进度
  viewServiceProgress: function() {
    wx.navigateTo({
      url: '/pages/service-progress/service-progress?serviceId=123'
    });
  }
});