<!--pages/shop-detail/shop-detail.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top:{{statusBarHeightRpx}}rpx;height:{{statusBarHeightRpx+88}}rpx;">
  <view class="navbar-content">
    <view class="navbar-left">
      <view class="nav-btn" bindtap="onBackTap">
        <image src="https://oss.csdu.net/ztl/images/arrow_left.png" class="back-icon"></image>
      </view>
      <view class="nav-btn" bindtap="onHomeTap">
        <image src="/icon/sy.png" class="home-icon"></image>
      </view>
    </view>
    <view class="navbar-title">汽车服务连锁机构</view>
    <view class="navbar-right">
      <text class="phone-number">400-0351-0</text>
    </view>
  </view>
</view>
<view class="navbar-spacer" style="height:{{statusBarHeightRpx+88}}rpx;"></view>

<!-- 页面主体 -->
<view class="page">
  <!-- 轮播图 -->
  <swiper class="banner-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}">
    <swiper-item wx:for="{{bannerList}}" wx:key="index">
      <image src="{{item.image}}" class="banner-image" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 门店信息卡片 -->
  <view class="shop-info-card">
    <!-- 基本信息区域 -->
    <view class="basic-info-section">
    <text class="shop-name">{{shopInfo.name}}</text>
    
    <!-- 评分和评价 -->
    <view class="shop-rating">
      <view class="rating-info">
        <image src="https://oss.csdu.net/ztl/images/star.png" class="star-icon"></image>
        <text class="rating-score">{{shopInfo.rating}}</text>
      </view>
      <text class="review-count">|  评价人数  {{shopInfo.reviewCount}}</text>
    </view>

    <!-- 营业状态 -->
    <view class="business-status">
      <text class="status-text">{{shopInfo.status}}</text>
      <view class="business-hours">
        <text class="hours-text">{{shopInfo.businessHours}}</text>
        </view>
      </view>
    </view>

    <!-- 地址和操作 -->
    <view class="shop-address-section">
      <view class="address-info">
        <text class="address-text">{{shopInfo.address}}</text>
        <text class="distance-text">{{shopInfo.distance}}</text>
      </view>
      <view class="address-actions">
        <view class="action-btn" bindtap="onNavigate">
          <image src="https://oss.csdu.net/ztl/images/daohang1.png" class="action-icon"></image>
          <text class="action-text">导航</text>
        </view>
        <view class="action-btn" bindtap="onCall">
          <image src="https://oss.csdu.net/ztl/images/dianhua1.png" class="action-icon"></image>
          <text class="action-text">电话</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 服务区域容器 -->
  <view class="services-container">
  <!-- 服务分类区域 -->
  <view class="service-filters">
    <view class="service-item" bindtap="onServiceTap" data-id="baoyang">
      <image wx:if="{{selectedService === 'baoyang'}}" src="https://oss.csdu.net/ztl/images/baoyang_select.png" class="service-image"></image>
      <image wx:else src="https://oss.csdu.net/ztl/images/baoyang_bg.png" class="service-image"></image>
    </view>
    <view class="service-item" bindtap="onServiceTap" data-id="xiche">
      <image wx:if="{{selectedService === 'xiche'}}" src="https://oss.csdu.net/ztl/images/xiche_select.png" class="service-image"></image>
      <image wx:else src="https://oss.csdu.net/ztl/images/xiche_bg.png" class="service-image"></image>
    </view>
    <view class="service-item" bindtap="onServiceTap" data-id="meirong">
      <image wx:if="{{selectedService === 'meirong'}}" src="https://oss.csdu.net/ztl/images/meiron_select.png" class="service-image"></image>
      <image wx:else src="https://oss.csdu.net/ztl/images/meirong_bg.png" class="service-image"></image>
    </view>
    <view class="service-item" bindtap="onServiceTap" data-id="weixiu">
      <image wx:if="{{selectedService === 'weixiu'}}" src="https://oss.csdu.net/ztl/images/weixiu_select.png" class="service-image"></image>
      <image wx:else src="https://oss.csdu.net/ztl/images/weixiu_bg.png" class="service-image"></image>
    </view>
  </view>

    <!-- 分隔线 -->
    <view class="divider-line"></view>

  <!-- 服务列表区域 -->
  <view class="services-section">
    <!-- 服务项目1 -->
    <view class="service-card">
      <view class="service-content">
        <view class="service-info">
          <image src="/images/shopdetail/car-wash-1.jpg" class="service-thumbnail" mode="aspectFill"></image>
          <view class="service-details">
            <text class="service-name">{{services[0].name}}</text>
            <text class="service-desc">{{services[0].description}}</text>
            <view class="service-price">
              <text class="price-symbol">￥</text>
              <text class="price-amount">{{services[0].currentPrice}}</text>
              <view class="original-price">
                <text class="original-label">原价</text>
                <text class="original-amount">￥{{services[0].originalPrice}}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="service-actions">
          <text class="sold-text">已售{{services[0].soldCount}}</text>
          <view class="buy-btn" bindtap="onBuyService" data-index="0">
            <text class="buy-text">抢购</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 服务项目2 -->
    <view class="service-card">
      <view class="service-content">
        <view class="service-info">
          <image src="/images/shopdetail/car-wash-2.jpg" class="service-thumbnail" mode="aspectFill"></image>
          <view class="service-details">
            <text class="service-name">{{services[1].name}}</text>
            <text class="service-desc">{{services[1].description}}</text>
            <view class="service-price">
              <text class="price-symbol">￥</text>
              <text class="price-amount">{{services[1].currentPrice}}</text>
              <view class="original-price">
                <text class="original-label">原价</text>
                <text class="original-amount">￥{{services[1].originalPrice}}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="service-actions">
          <text class="sold-text">已售{{services[1].soldCount}}</text>
          <view class="buy-btn" bindtap="onBuyService" data-index="1">
            <text class="buy-text">抢购</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
