import config from './config'
import tokenManager from './tokenManager'
import { validateTokenAndHandle } from './auth.js'

const request = async (options) => {
  return new Promise((resolve, reject) => {
    const token = tokenManager.getToken();
    
    // 构建完整的请求URL
    let fullUrl = options.url;
    if (!options.url.startsWith('http')) {
      // 修复URL拼接，避免重复斜杠
      const baseUrl = config.baseUrl.endsWith('/') ? config.baseUrl.slice(0, -1) : config.baseUrl;
      const requestUrl = options.url.startsWith('/') ? options.url.slice(1) : options.url;
      fullUrl = `${baseUrl}/${requestUrl}`;
    }
    
    // 构建请求头
    const headers = {
      'content-type': 'application/json',
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    wx.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data,
      header: headers,
      success: (res) => {
        if (res.statusCode === 200) {
          // 检查业务状态码
          if (res.data && (res.data.code === 401 || res.data.code === 403)) {
            // token过期，使用新的验证函数处理
            validateTokenAndHandle({
              showExpiredToast: true,
              expiredMessage: '登录已过期，请重新登录',
              redirectToLogin: true
            });
            reject(new Error('TOKEN_EXPIRED'));
            return;
          }
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // HTTP 401，token过期
          validateTokenAndHandle({
            showExpiredToast: true,
            expiredMessage: '登录已过期，请重新登录',
            redirectToLogin: true
          });
          reject(new Error('TOKEN_EXPIRED'));
        } else {
          console.error(`API错误: ${res.statusCode}`, res);
          reject(res);
        }
      },
      fail: (err) => {
        console.error(`网络请求失败: ${fullUrl}`, err);
        
        // 网络错误友好提示
        if (err.errMsg && err.errMsg.includes('timeout')) {
          wx.showToast({
            title: '网络请求超时，请重试',
            icon: 'none'
          });
        } else if (err.errMsg && err.errMsg.includes('fail')) {
          wx.showToast({
            title: '网络连接失败，请检查网络',
            icon: 'none'
          });
        }
        
        reject(err);
      }
    })
  })
}

export default request 