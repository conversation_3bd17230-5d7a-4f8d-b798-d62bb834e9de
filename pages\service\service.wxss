/* pages/service/service.wxss */
.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  object-fit: cover;
}


/* 页面标题样式 */
.page-header {
  width: 750rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-title {
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
}

/* 车辆信息区域样式 */
.vehicle-section {
  width: 706rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  margin: 20rpx 22rpx 0 22rpx;
  background-color: #FFFFFF;
  border-radius: 25rpx;
  padding: 40rpx 26rpx 26rpx 26rpx;
  box-sizing: border-box;
}

.vehicle-label {
  width: 120rpx;
  height: 40rpx;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  z-index: 10;
}

.vehicle-label-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.vehicle-label-text {
  position: relative;
  z-index: 2;
  color: #FFFFFF;
  font-size: 18rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 18rpx;
  max-width: 90rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8rpx 0 0 15rpx;
}

/* 车辆主要信息区域 */
.vehicle-main-info {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-bottom: 26rpx;
}

.vehicle-image {
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.vehicle-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.model-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 21rpx;
}

.model-name {
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-right: 26rpx;
}

.model-tag {
  width: 27rpx;
  height: 27rpx;
}

.model-specs {
  overflow-wrap: break-word;
  color: rgba(139,139,139,1);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}

.info-icon {
  width: 39rpx;
  height: 36rpx;
  margin-left: 20rpx;
  flex-shrink: 0;
  align-self: center;
}
/* 车牌信息区域样式 */
.plate-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.plate-display {
  background-color: rgba(86,38,162,1.000000);
  border-radius: 8rpx;
  width: 145rpx;
  height: 35rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.plate-prefix {
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 23rpx;
  font-family: MicrosoftYaHei-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 23rpx;
  margin-left: 8rpx;
}

.plate-suffix {
  overflow-wrap: break-word;
  color: rgba(57,22,108,1);
  font-size: 23rpx;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 23rpx;
  margin-left: 6rpx;
}

.auth-container {
  background-color: rgba(225,227,234,1.000000);
  border-radius: 8rpx;
  height: 35rpx;
  width: 98rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-status {
  overflow-wrap: break-word;
  color: rgba(62,65,88,1);
  font-size: 23rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 23rpx;
}

/* 服务记录标题样式 */
.service-record-header {
  width: 167rpx;
  height: 31rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 42rpx 0 0 22rpx;
}

.record-icon {
  width: 31rpx;
  height: 31rpx;
}

.record-title {
  width: 125rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}
/* 服务记录列表样式 */
.service-record-list {
  width: 706rpx;
  height: 627rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 20rpx 22rpx 0 22rpx;
}

/* 服务记录项样式 */
.service-record-item {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 25rpx;
  width: 706rpx;
  height: 304rpx;
  margin-bottom: 19rpx;
  display: flex;
  flex-direction: column;
}

/* 记录头部样式 */
.record-header {
  width: 657rpx;
  height: 42rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 31rpx 0 0 26rpx;
}

/* 记录时间样式 */
.record-time {
  width: 338rpx;
  height: 26rpx;
  margin-top: 7rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}

.time-icon {
  width: 29rpx;
  height: 26rpx;
}

.time-text {
  width: 295rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgba(139,139,139,1);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
}

/* 记录状态样式 */
.record-status {
  background-color: rgba(255,243,224,1.000000);
  border-radius: 8rpx;
  height: 42rpx;
  display: flex;
  flex-direction: column;
  width: 98rpx;
}

.status-text {
  width: 68rpx;
  height: 26rpx;
  overflow-wrap: break-word;
  color: rgba(255,152,0,1);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 26rpx;
  margin: 8rpx 0 0 15rpx;
}

/* 服务项目样式 */
.service-project {
  width: 653rpx;
  height: 31rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 26rpx;
}

.project-label {
  width: 41rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(139,139,139,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.project-name {
  width: 298rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

/* 门店信息样式 */
.store-info {
  width: 653rpx;
  height: 31rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 26rpx;
}

.store-label {
  width: 41rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(139,139,139,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.store-name {
  width: 339rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.store-image {
  width: 31rpx;
  height: 31rpx;
}

/* 操作按钮样式 */
.action-buttons {
  width: 653rpx;
  height: 70rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 20rpx 0 0 26rpx;
}

.navigate-btn {
  background-color: rgba(63,103,255,1.000000);
  border-radius: 35rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 195rpx;
}

.progress-btn {
  background-color: rgba(158,147,255,1.000000);
  border-radius: 35rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 195rpx;
}

.book-btn, .modify-btn {
  background-color: rgba(255,255,255,1.000000);
  border: 2rpx solid rgba(63,103,255,1.000000);
  border-radius: 35rpx;
  height: 70rpx;
  display: flex;
  flex-direction: column;
  width: 195rpx;
}

.btn-text {
  width: 125rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(63,103,255,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 19rpx 0 0 35rpx;
}

.navigate-btn .btn-text {
  color: rgba(255,255,255,1);
}

.progress-btn .btn-text {
  color: rgba(255,255,255,1);
}