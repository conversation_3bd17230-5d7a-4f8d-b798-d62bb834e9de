



.page {
  position: relative;
  width: 750rpx;
  min-height: 100vh;   /* 允许内容超出时滚动 */
  overflow: auto;
  display: flex;
  flex-direction: column;
  background: #F5F6FA; /* 统一背景色 */
  padding-bottom: 120rpx; /* 添加底部内边距，避免内容紧贴底部 */
}

.section_1 {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}

.section_1::before {
  content: '';
  position: absolute;
  top: 0; /* 从页面顶部开始 */
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}
.box_1 {
  position: relative;
  width: 750rpx;
  height: 284rpx;
  display: flex;
  flex-direction: column;
  background: transparent; /* 让背景透明，继承父元素的背景色 */
}
.box_2 {
  width: 692rpx;
  height: 251rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 1000;
  margin: 13rpx 0 0 26rpx;
}
.text_1 {
  width: 162rpx;
  height: 33rpx;
  overflow-wrap: break-word;
  color: #252027;
  font-size: 32rpx;
  font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  font-weight: bold;
  text-align: center;
  line-height: 16rpx;
  margin-top: 33rpx;
}
.image-wrapper_1 {
  position: relative;
  z-index: 9999;
  height: 251rpx;
  display: flex;
  flex-direction: column;
  width: 236rpx;
}

.image_1 {
  width: 236rpx;
  height: 251rpx;
  display: block;
  margin: 0 auto;
  object-fit: contain; /* 保证图片完整显示不变形 */
}

.image_2 {
  width: 161rpx;
  height: 63rpx;
  margin: 6rpx 0 0 91rpx;
  position: relative;
  z-index: 999;
}
.box_3 {
  background-color: rgba(245,244,252,1.000000);
  border-radius: 36rpx;
  position: absolute;
  left: 26rpx;
  top: 104rpx;
  width: 474rpx;
  height: 72rpx;
  border: 1px solid rgba(214,218,224,1);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20rpx;
}
.image-text_1 {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}
.image_3 {
  width: 28rpx;
  height: 28rpx;
  margin: 0 8rpx 0 0;
}
.text-group_1 {
  height: 28rpx;
  color: rgba(37,32,39,1.000000);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 28rpx;
}
.image_4 {
  width: 2rpx;
  height: 28rpx;
  margin: 0 16rpx 0 0;
}
.text_2 {
  height: 28rpx;
  color: rgba(75,80,96,1.000000);
  font-size: 26rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 28rpx;
}
.box_4 {
  position: absolute;
  left: 0rpx;
  top: 432rpx;
  width: 750rpx;
  height: auto;
  background: url("https://oss.csdu.net/ztl/images/home_bg.png") 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
}
.list_1 {
  width: 602rpx;
  height: 111rpx;
  flex-direction: row;
  display: flex;
  margin: 0 0 0 72rpx;
}
.image-text_2-0 {
  width: 91rpx;
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 3rpx 86rpx 0 0;
}
.image_5-0 {
  width: 91rpx;
  height: 66rpx;
}
.text-group_2-0 {
  width: 56rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(44,44,44,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 15rpx 0 0 17rpx;
}
.image-text_2-1 {
  width: 91rpx;
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 3rpx 86rpx 0 0;
}
.image_5-1 {
  width: 91rpx;
  height: 66rpx;
}
.text-group_2-1 {
  width: 56rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(44,44,44,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 15rpx 0 0 17rpx;
}
.image-text_2-2 {
  width: 91rpx;
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 3rpx 86rpx 0 0;
}
.image_5-2 {
  width: 91rpx;
  height: 66rpx;
}
.text-group_2-2 {
  width: 56rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(44,44,44,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 15rpx 0 0 17rpx;
}
.image-text_2-3 {
  width: 91rpx;
  height: 108rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 3rpx 86rpx 0 0;
}
.image_5-3 {
  width: 91rpx;
  height: 66rpx;
}
.text-group_2-3 {
  width: 56rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(44,44,44,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 15rpx 0 0 17rpx;
}
.box_5 {
  height: 299rpx;
  background: url("https://oss.csdu.net/ztl/images/home_blue.png") 100% no-repeat;
  background-size: 100% 100%;
  width: 704rpx;
  display: flex;
  flex-direction: column;
  margin: 44rpx 0 0 23rpx;
}
.box_6 {
  width: 643rpx;
  height: 31rpx;
  flex-direction: row;
  display: flex;
  margin: 22rpx 0 0 25rpx;
}
.text_3 {
  width: 125rpx;
  height: 31rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 32rpx;
  font-family: Alibaba-PuHuiTi-B;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}
.text_4 {
  width: 96rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 3rpx 0 0 390rpx;
}
.thumbnail_1 {
  width: 12rpx;
  height: 20rpx;
  margin: 4rpx 0 0 20rpx;
}
.image-wrapper_2 {
  width: 672rpx;
  height: 165rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 14rpx 0 0 15rpx;
}
.image_6 {
  width: 332rpx;
  height: 165rpx;
}
.image_7 {
  width: 162rpx;
  height: 165rpx;
  margin-left: 8rpx;
}
.image_8 {
  width: 162rpx;
  height: 165rpx;
  margin-left: 8rpx;
}
.box_7 {
  width: 567rpx;
  height: 20rpx;
  flex-direction: row;
  display: flex;
  margin: 23rpx 0 24rpx 68rpx;
}
.text_5 {
  width: 115rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}
.thumbnail_2 {
  width: 15rpx;
  height: 15rpx;
  margin: 2rpx 0 0 17rpx;
}
.text_6 {
  width: 102rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin-left: 18rpx;
}
.thumbnail_3 {
  width: 15rpx;
  height: 15rpx;
  margin: 2rpx 0 0 21rpx;
}
.text_7 {
  width: 102rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin-left: 14rpx;
}
.thumbnail_4 {
  width: 15rpx;
  height: 15rpx;
  margin: 2rpx 0 0 18rpx;
}
.text_8 {
  width: 103rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(245,244,249,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin-left: 12rpx;
}
.image-wrapper_3 {
  width: 706rpx;
  height: 128rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 26rpx 0 0 21rpx;
}
.image_9 {
  width: 348rpx;
  height: 128rpx;
}
.image_10 {
  width: 349rpx;
  height: 128rpx;
}
.box_8 {
  width: 750rpx;
  height: 90rpx;
  margin: 35rpx 0 0 0;
  overflow: hidden;
  position: relative;
}

.service-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
}

.service-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 23rpx;
  padding-right: 23rpx;
  min-width: 100%;
}
.box_9 {
  background-color: rgba(226,224,244,1.000000);
  border-radius: 25rpx;
  position: relative;
  width: 169rpx;
  height: 90rpx;
  flex-direction: row;
  display: flex;
  flex-shrink: 0;
}
.text-wrapper_1 {
  width: 115rpx;
  height: 51rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 21rpx 0 0 20rpx;
}
.text_9 {
  width: 54rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(30,30,30,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
}
.text_10 {
  width: 114rpx;
  height: 15rpx;
  overflow-wrap: break-word;
  color: rgba(161,170,190,1);
  font-size: 18rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 9rpx 0 0 1rpx;
}
.image_11 {
  width: 83rpx;
  height: 78rpx;
  margin: 12rpx 1rpx 0 -50rpx;
}
.image_12 {
  position: absolute;
  left: 0rpx;
  top: 0rpx;
  width: 169rpx;
  height: 90rpx;
}
.service-item {
  width: 169rpx;
  height: 90rpx;
  margin-left: 9rpx;
  flex-shrink: 0;
}

.service-image {
  width: 169rpx;
  height: 90rpx;
}

.list_2 {
  width: 703rpx;
  height: 800rpx; /* 固定高度，避免跳动 */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin: 23rpx 0 0 22rpx;
  overflow: hidden; /* 隐藏溢出内容 */
  position: relative; /* 为内部滚动做准备 */
}
/* 通用商品列表项样式 */
.list-items {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 20rpx;
  width: 703rpx;
  height: 224rpx;
  margin-bottom: 20rpx;
  flex-direction: row;
  display: flex;
  align-items: center; /* 恢复居中对齐 */
  padding: 0 24rpx 0 0; /* 添加右边距 */
  box-sizing: border-box;
}
.product-box {
  background-color: rgba(102,119,179,1.000000);
  border-radius: 20rpx;
  height: 223rpx;
  display: flex;
  flex-direction: column;
  width: 247rpx;
  position: relative;
  flex-shrink: 0; /* 防止图片区域被压缩 */
}
.product-image {
  width: 247rpx;
  height: 223rpx;
  border-radius: 20rpx;
  object-fit: cover;
}
.product-tag {
  background-color: rgba(198,200,255,1.000000);
  border-radius: 20rpx;
  height: 33rpx;
  display: flex;
  flex-direction: column;
  width: 120rpx;
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  z-index: 1;
}
.product-tag-text {
  width: 78rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(30,30,66,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 7rpx 0 0 23rpx;
}
.product-info {
  flex: 1; /* 让信息区域自适应剩余空间 */
  height: 168rpx;
  display: flex;
  flex-direction: column;
  margin: 0rpx 0 0 20rpx; /* 进一步减少上边距，让标题位置再往上移动 */
  padding-top: 0rpx; /* 移除顶部内边距 */
  overflow: visible; /* 改为visible，允许内容正常显示 */
  min-width: 0; /* 允许内容收缩 */
  position: relative; /* 为buy-button定位做准备 */
}
.product-title {
  width: 100%; /* 使用100%宽度，充分利用可用空间 */
  height: 32rpx; /* 恢复原来的高度 */
  overflow: hidden;
  color: rgba(30,30,30,1);
  font-size: 32rpx;
  font-family: PingFang-SC-Heavy;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  text-overflow: ellipsis; /* 添加省略号 */
  line-height: 32rpx; /* 恢复原来的行高 */
  margin: -6rpx 0 0 0; /* 只让标题往上移动 */
  padding-right: 140rpx; /* 为buy-button预留空间 */
}
.product-tags {
  width: 100%; /* 使用100%宽度 */
  height: 32rpx;
  margin-top: 16rpx;
  flex-direction: row;
  display: flex;
  gap: 8rpx; /* 使用gap来控制标签间距，替代justify-content: space-between */
}
.tag-wrapper {
  background-color: rgba(232,232,255,1.000000);
  border-radius: 10rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  width: 106rpx;
}
.tag-text {
  width: 77rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(62,61,68,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 6rpx 0 0 13rpx;
}
.tag-wrapper-wide {
  background-color: rgba(232,232,255,1.000000);
  border-radius: 10rpx;
  height: 32rpx;
  display: flex;
  flex-direction: column;
  width: 126rpx;
}
.tag-text-wide {
  width: 97rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(62,61,68,1);
  font-size: 20rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 16rpx;
  margin: 6rpx 0 0 14rpx;
}
.price-wrapper {
  width: 100%; /* 使用100%宽度 */
  height: 33rpx;
  flex-direction: row;
  display: flex;
  align-items: baseline;
  margin: 16rpx 0 0 0; /* 移除左边距 */
}
.price-symbol {
  width: 15rpx;
  height: 18rpx;
  overflow-wrap: break-word;
  color: rgba(255,64,101,1);
  font-size: 24rpx;
  font-family: HarmonyOS_Sans_SC_Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 42rpx;
  margin: 0;
}
.price-main {
  width: auto;
  min-width: 48rpx;
  height: 33rpx;
  overflow-wrap: break-word;
  color: rgba(255,64,101,1);
  font-size: 42rpx;
  font-family: HarmonyOS_Sans_SC_Bold;
  font-weight: 700;
  text-align: left;
  line-height: 42rpx;
  margin: 0 0 0 8rpx;
}
.price-original {
  width: 91rpx;
  height: 22rpx;
  overflow-wrap: break-word;
  color: rgba(62,61,68,1);
  font-size: 22rpx;
  font-family: PingFang-SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42rpx;
  margin: 0 0 0 12rpx;
}
.member-price-wrapper {
  height: 32rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%; /* 使用100%宽度 */
  position: relative;
  margin: 16rpx 0 0 0;
}
.member-price-text-wrapper {
  width: auto;
  min-width: 164rpx;
  height: 20rpx;
  overflow-wrap: break-word;
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20rpx;
  margin: 0;
  padding: 0 8rpx;
}
.member-price-text {
  width: 100%;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save-price-bg {
  background-color: rgba(235,230,244,1.000000);
  border-radius: 0 16rpx 16rpx 0;
  width: auto;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: -16rpx;
  z-index: 1;
}
.save-price-text {
  width: auto;
  height: 20rpx;
  overflow-wrap: break-word;
  color: rgba(255, 64, 101, 1);
  font-size: 20rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  padding: 0 18rpx 0 24rpx;
  display: flex;
  align-items: center;
}
.member-price-badge {
  background-color: rgba(48,52,67,1.000000);
  border-radius: 16rpx;
  position: relative;
  width: auto;
  min-width: 85rpx;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 0;
  z-index: 2;
}
.buy-button {
  height: 64rpx;
  background-size: 100% 100%;
  display: flex;
  flex-direction: row;
  width: 128rpx;
  position: absolute; /* 改为绝对定位 */
  right: 0; /* 定位到product-info的右边 */
  bottom: 0; /* 定位到底部，与原来的位置保持一致 */
  background: linear-gradient(135deg, #3F67FF 0%, #5C299E 100%);
  border-radius: 32rpx;
  align-items: center;
  justify-content: center;
}
.buy-button-text {
  width: 54rpx;
  height: 28rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1);
  font-size: 28rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 0;
}

/* 商品列表滚动容器 */
.products-scroll-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* iOS流畅滚动 */
}

/* 底部分割线和提示样式 */
.list-footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
  margin-bottom: 80rpx; /* 增加底部距离，与导航栏保持距离 */
}

.no-more-text {
  color: rgba(161, 170, 190, 1);
  font-size: 24rpx;
  font-family: PingFang-SC-Regular;
  font-weight: normal;
  text-align: center;
  line-height: 24rpx;
  position: relative;
  padding: 0 20rpx; /* 文字两边留空间 */
}

.no-more-text::before,
.no-more-text::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 100rpx; /* 增加线条长度 */
  height: 1rpx;
  background-color: rgba(232, 232, 255, 1);
  transform: translateY(-50%);
}

.no-more-text::before {
  left: -120rpx; /* 调整左边线条位置 */
}

.no-more-text::after {
  right: -120rpx; /* 调整右边线条位置 */
}
/* 第二个商品项的样式已删除，现在使用通用样式 */
.box_15 {
  position: static;
  position: relative;
  z-index: 1;
  height: 192rpx;
  background: url("https://oss.csdu.net/ztl/images/home_top_bg.png") 100% no-repeat;
  background-size: 100% 100%;
  width: 704rpx;
  position: absolute;
  left: 22rpx;
  top: 200rpx;
  display: flex;
  flex-direction: column;
  z-index: 1;
}
.text-wrapper_8 {
  width: 330rpx;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  margin: 31rpx 0 0 34rpx;
}
.text_22 {
  width: 330rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: MicrosoftYaHei-Bold;
  font-weight: 700;
  text-align: center;
  line-height: 16rpx;
}
.box_16 {
  width: 617rpx;
  height: 81rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 13rpx 0 35rpx 45rpx;
}
.list_3 {
  width: 405rpx;
  height: 81rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.image-text_8-0 {
  position: relative;
  width: 95rpx;
  height: 77rpx;
  display: flex;
  flex-direction: column;
  margin: 4rpx 59rpx 0 0;
}
.label_4-0 {
  position: absolute;
  left: 24rpx;
  top: 0rpx;
  width: 45rpx;
  height: 40rpx;
}
.text-group_8-0 {
  width: 96rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(50,47,47,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
  margin-top: 54rpx;
}
.image-text_8-1 {
  position: relative;
  width: 95rpx;
  height: 77rpx;
  display: flex;
  flex-direction: column;
  margin: 4rpx 59rpx 0 0;
}
.label_5-1 {
  position: absolute;
  left: 34rpx;
  top: 0rpx;
  width: 31rpx;
  height: 40rpx;
}
.text-group_8-1 {
  width: 96rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(50,47,47,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
  margin-top: 54rpx;
}
.image-text_8-2 {
  position: relative;
  width: 95rpx;
  height: 77rpx;
  display: flex;
  flex-direction: column;
  margin: 4rpx 59rpx 0 0;
}
.label_4-2 {
  position: absolute;
  left: 24rpx;
  top: 0rpx;
  width: 45rpx;
  height: 40rpx;
}
.text-group_8-2 {
  width: 96rpx;
  height: 23rpx;
  overflow-wrap: break-word;
  color: rgba(50,47,47,1);
  font-size: 24rpx;
  font-family: PingFang-SC-Bold;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16rpx;
  margin-top: 54rpx;
}
.text-wrapper_9 {
  height: 64rpx;
  background: url("https://oss.csdu.net/ztl/images/home_blue_button.png") 100% no-repeat;
  background-size: 100% 100%;
  margin-top: 8rpx;
  display: flex;
  flex-direction: column;
  width: 158rpx;
  align-items: center;
  justify-content: center;
}
.text_23 {
  width: 111rpx;
  height: 27rpx;
  overflow-wrap: break-word;
  color: rgba(254,254,254,1);
  font-size: 28rpx;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 27rpx;
  margin: 0;
}
.image_18 {
  position: absolute;
  left: 477rpx;
  top: 177rpx;
  width: 257rpx;
  height: 90rpx;
  z-index: 10;
}

/* 弹窗遮罩层 */
.popup-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.5); /* 50%透明度灰色 */
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 弹窗内容居中 */
.popup-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 主图片样式 */
.popup-img {
  width: 480rpx; /* 可根据实际图片宽度调整 */
  border-radius: 16rpx;
}

/* 关闭按钮样式 */
.popup-close {
  width: 80rpx; /* 可根据实际图片宽度调整 */
  margin-top: 64rpx;
}

/* 已登录状态的车辆信息样式 */
.vehicle-info-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 31rpx 34rpx 35rpx 6rpx;
}

.vehicle-header {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  margin-bottom: 20rpx;
  margin-left: 34rpx;
}

.vehicle-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.vehicle-brand-model {
  color: rgba(37,32,39,1);
  font-size: 32rpx;
  font-family: MicrosoftYaHei-Bold;
  font-weight: 700;
  line-height: 32rpx;
}

.dropdown-icon {
  width: 23rpx;
  height: 14rpx;
  margin-left: 15rpx;
}

.plate-number-tag {
  display: flex;
  background: #5626A2;
  border-radius: 8rpx;
  padding: 2rpx;
  overflow: visible;
  margin-left: 28rpx;
  height: 30rpx;
  box-sizing: border-box;
  min-width: 120rpx;
  z-index: 10;
  position: relative;
}

.plate-prefix {
  color: #FFFFFF;
  font-size: 20rpx;
  font-family: MicrosoftYaHei;
  font-weight: Bold;
  margin-right: 4rpx;
  margin-left: 4rpx;
  height: 26rpx;
  line-height: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  min-width: 0;
  flex-shrink: 0;
}

.plate-number {
  background: #FFFFFF;
  color: #39166C;
  font-size: 20rpx;
  font-weight: Regular;
  padding: 0rpx 2rpx;
  height: 26rpx;
  line-height: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  white-space: nowrap;
}

.vehicle-stats {
  display: flex;
  gap: 60rpx;
  margin-bottom: 20rpx;
  margin-left: 34rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  color: #8B5CF6;
  font-size: 36rpx;
  font-weight: 700;
  line-height: 36rpx;
}

.stat-label {
  color: rgba(37,32,39,1);
  font-size: 24rpx;
  font-weight: 500;
  line-height: 24rpx;
}

.vehicle-icon {
  position: absolute;
  right: 34rpx;
  top: 60%;
  transform: translateY(-50%);
}

.car-icon {
  width: 100rpx;
  height: 80rpx;
  object-fit: contain;
}