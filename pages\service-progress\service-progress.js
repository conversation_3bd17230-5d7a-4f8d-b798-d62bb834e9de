Page({
  data: {
    statusBarHeight: 44, // 默认值，动态获取
    serviceProgress: {
      currentStep: 2, // 当前进度步骤
      totalSteps: 4,  // 总步骤数
      steps: [
        { name: '在线预约', status: 'completed' },
        { name: '到店服务', status: 'completed' },
        { name: '技师服务', status: 'current' },
        { name: '技术服务', status: 'pending' }
      ]
    }
  },
  
  onLoad: function(options) {
    const statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
    this.setData({ statusBarHeight });
    console.log('服务进度页面加载成功');
    
    // 如果有传入的服务ID，可以在这里获取服务详情
    if (options.serviceId) {
      this.getServiceProgress(options.serviceId);
    }
  },

  onShow() {
    // 页面显示时的逻辑
  },

  // 获取服务进度
  getServiceProgress: function(serviceId) {
    // 这里可以调用API获取服务进度
    console.log('获取服务进度:', serviceId);
    
    // 模拟API调用
    wx.showLoading({
      title: '加载中...'
    });
    
    setTimeout(() => {
      wx.hideLoading();
      // 更新进度数据
      this.setData({
        'serviceProgress.currentStep': 2
      });
    }, 1000);
  },

  // 刷新进度
  refreshProgress: function() {
    console.log('刷新服务进度');
    
    wx.showLoading({
      title: '刷新中...'
    });
    
    // 模拟刷新操作
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '进度已更新',
        icon: 'success'
      });
    }, 1500);
  },

  // 修改预约
  modifyAppointment: function() {
    console.log('修改预约');
    
    wx.showModal({
      title: '修改预约',
      content: '是否要修改当前预约时间？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/appointment/appointment?mode=modify'
          });
        }
      }
    });
  },

  // 导航到门店
  navigateToStore: function() {
    console.log('导航到门店');
    
    wx.showToast({
      title: '导航功能开发中',
      icon: 'none'
    });
  },

  // 联系客服
  contactService: function() {
    console.log('联系客服');
    
    wx.showActionSheet({
      itemList: ['拨打电话', '在线客服'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 拨打电话
          wx.makePhoneCall({
            phoneNumber: '************'
          });
        } else if (res.tapIndex === 1) {
          // 在线客服
          wx.showToast({
            title: '在线客服功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 分享服务进度
  shareProgress: function() {
    console.log('分享服务进度');
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
}); 