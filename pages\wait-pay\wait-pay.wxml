<!--pages/wait-pay/wait-pay.wxml-->
<!-- 自定义导航栏 -->
<view class="custom-navbar" style="padding-top:{{statusBarHeightRpx}}rpx;height:{{statusBarHeightRpx+88}}rpx;">
  <view class="navbar-content">
    <view class="navbar-left">
      <view class="nav-btn" bindtap="onBackTap">
        <image src="https://oss.csdu.net/ztl/images/arrow_left.png" class="back-icon"></image>
      </view>
      <view class="nav-btn" bindtap="onHomeTap">
        <image src="/icon/sy.png" class="home-icon"></image>
      </view>
    </view>
    <view class="navbar-title">待支付</view>
    <view class="navbar-right"></view>
  </view>
</view>
<view class="navbar-spacer" style="height:{{statusBarHeightRpx+88}}rpx;"></view>

<!-- 页面内容 -->
<view class="page">
  <!-- 倒计时区域 -->
  <view class="countdown-section">
    <view class="countdown-content">
      <text class="countdown-text">等待您的付款:剩</text>
      <text class="countdown-time">{{countdown.hours}}</text>
      <text class="countdown-unit">小时</text>
      <text class="countdown-time">{{countdown.minutes}}</text>
      <text class="countdown-unit">分</text>
      <text class="countdown-time">{{countdown.seconds}}</text>
      <text class="countdown-text">秒自动关闭</text>
    </view>
  </view>

  <!-- 订单信息区域 -->
  <view class="order-section">
    <!-- 门店信息 -->
    <view class="shop-card">
      <view class="shop-info">
        <image src="https://oss.csdu.net/ztl/images/mendian1.png" class="shop-icon"></image>
        <view class="shop-details">
          <text class="shop-name">{{shopInfo.name}}</text>
          <view class="shop-address">
            <text class="address-text">{{shopInfo.address}}</text>
            <text class="address-detail">{{shopInfo.addressDetail}}</text>
          </view>
        </view>
      </view>
      <view class="shop-actions">
        <view class="action-btn" bindtap="onNavigate">
          <image src="https://oss.csdu.net/ztl/images/daohang1.png" class="action-icon"></image>
          <text class="action-text">导航</text>
        </view>
        <view class="action-btn" bindtap="onCall">
          <image src="https://oss.csdu.net/ztl/images/dianhua1.png" class="action-icon"></image>
          <text class="action-text">电话</text>
        </view>
      </view>
    </view>

    <!-- 服务信息 -->
    <view class="service-card">
      <view class="service-info">
        <view class="service-icon"></view>
        <view class="service-details">
          <text class="service-name">{{orderInfo.serviceName}}</text>
          <view class="service-price">
            <text class="price-symbol">￥</text>
            <text class="price-amount">{{orderInfo.finalPrice}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 价格明细 -->
    <view class="price-card">
      <text class="price-title">价格明细</text>
      <view class="price-details">
        <view class="price-row">
          <text class="price-label">订单总价</text>
          <text class="price-value">￥{{orderInfo.originalPrice}}</text>
        </view>
        <view class="price-row">
          <text class="price-label">优惠券</text>
          <text class="price-discount">-￥{{orderInfo.discountAmount}}</text>
        </view>
      </view>
      <view class="price-divider"></view>
      <view class="price-summary">
        <view class="summary-row">
          <text class="summary-label">已优惠：</text>
          <text class="summary-value">￥{{orderInfo.discountAmount}}</text>
        </view>
        <view class="summary-row">
          <text class="summary-label">实付款:</text>
          <text class="summary-total">￥{{orderInfo.finalPrice}}</text>
        </view>
      </view>
    </view>

    <!-- 订单编号 -->
    <view class="order-number-card">
      <view class="order-number-wrapper">
        <text class="order-number">订单编号：{{orderInfo.orderNo}}</text>
        <image src="https://oss.csdu.net/ztl/images/arrow_down.png" class="copy-icon"></image>
      </view>
      <view class="copy-btn" bindtap="onCopyOrderNo">
        <text class="copy-text">复制单号</text>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="action-section">
    <view class="cancel-btn" bindtap="onCancelOrder">
      <text class="btn-text">取消订单</text>
    </view>
    <view class="pay-btn" bindtap="onPay">
      <text class="btn-text">立即付款</text>
    </view>
  </view>
</view>
