import { createPage } from '../../utils/pageBase.js';
import smartRequest from '../../utils/smartRequest.js';

Page(createPage({
  data: {
    vehicles: [],
    isLoggedIn: false,
    loading: false,
    statusBarHeight: 44, // 默认状态栏高度
    products: [],
    selectedService: 'tuijian' // 当前选中的服务类型
  },

  onLoad(options) {
    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });
    
    // 基类已经处理了页面类型检查
    this.loadHomeData();
    // 加载推荐商品
    this.loadProducts('tuijian');
  },

  // 获取默认车辆
  async getDefaultVehicle() {
    try {
      const result = await smartRequest({
        url: '/vehicle/default',
        method: 'GET'
      }, null);
      
      if (result.code === 200) {
        // 有默认车辆，返回车辆数据
        return result.data;
      } else if (result.code === 1) {
        // 未设置默认车辆，返回null
        return null;
      }
    } catch (error) {
      console.error('获取默认车辆失败:', error);
      return null; // 任何错误都返回null，显示添加爱车界面
    }
  },

  async loadHomeData() {
    // 获取默认车辆
    const defaultVehicle = await this.getDefaultVehicle();
    // 转换API数据格式为页面需要的格式
    const vehicleData = defaultVehicle ? {
      id: defaultVehicle.id,
      plate_number: defaultVehicle.licensePlate,
      plate_prefix: defaultVehicle.licensePlate.substring(0, 2),
      plate_suffix: defaultVehicle.licensePlate.substring(2),
      brand: defaultVehicle.vehicleBrand,
      model: defaultVehicle.vehicleModel,
      color: '白色', // API中没有颜色字段，暂时设为白色
      maintenance_records: 0, // API中没有这个字段，暂时设为0
      coupons: 28 // API中没有这个字段，暂时设为28
    } : null;
    

    
    this.setData({
      vehicles: vehicleData ? [vehicleData] : [],
      isLoggedIn: !!vehicleData, // 根据是否有车辆数据判断登录状态
      loading: false
    });
  },

  // 添加车辆
  handleAddVehicle() {
    const auth = require('../../utils/auth.js');
    
    if (!auth.isLoggedIn()) {
      // 未登录，跳转到登录页
      auth.redirectToLoginPage();
      return;
    }
    
    // 已登录但没有默认车辆，跳转到绑定车辆页面
    wx.navigateTo({
      url: '/pages/bind-car/bind-car'
    });
  },

  // 查看车辆详情
  handleVehicleDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/vehicle-detail/vehicle-detail?id=${id}`
    });
  },

  // 加载商品数据
  async loadProducts(serviceType) {
    try {
      let url, params;
      
      if (serviceType === 'tuijian') {
        // 推荐商品
        url = '/spu/recommend';
        params = {};
      } else {
        // 其他分类商品
        url = '/spu/list';
        params = { cid: serviceType };
      }
      
      const result = await smartRequest({
        url: url,
        method: 'GET',
        data: params
      });
      
      if (result.code === 200 && result.data && result.data.items) {
         // 转换API数据格式
         const products = result.data.items.map(item => {
          // 解析图片URL
          let imageUrl = '';
          if (item.photo) {
            try {
              const photoData = JSON.parse(item.photo);
              imageUrl = photoData.length > 0 ? photoData[0] : '';
            } catch (e) {
              imageUrl = item.photo;
            }
          }
          
          return {
            id: item.id,
            image: imageUrl,
            tag: '标准洗车', // 暂时固定，API中没有此字段
            title: item.name,
            tags: ['门店通用', '节假日通用'], // 暂时固定，API中没有此字段
            price: item.vip_price,
            originalPrice: item.price,
            savePrice: item.vip_savings
          };
        });
        
        this.setData({
          products: products
        });
      }
    } catch (error) {
      console.error('加载商品数据失败:', error);
    }
  },

  // 处理服务分类点击
  onServiceTap(e) {
    const { id } = e.currentTarget.dataset;
    
    this.setData({
      selectedService: id
    });
    
    // 加载商品数据
    this.loadProducts(id);
  },

  // 处理服务分类点击
  handleServiceCategoryTap(e) {
    const { value } = e.currentTarget.dataset;
    console.log('选择的服务分类:', value);
    // 后续可以在这里根据 value 进行过滤操作
    // 1=保养 2=洗护 3=维修 4=美容
  },

}));
